const { processTweetImages } = require('../utils/twitterPostImageProcessor');
const { uploadImageToGCS } = require('../utils/storage');
const imageSize = require('image-size');
const axios = require('axios');

jest.mock('../utils/storage');
jest.mock('axios');
jest.mock('image-size');

const dummyArticleId = 'dummy-article-id';

describe('Tweet Image Processing', () => {
    afterEach(() => {
        jest.clearAllMocks();
    });

    it('Relevant image present and uploaded successfully', async () => {
        const imageUrl = 'https://image.com/image.jpg';
        axios.get.mockResolvedValue({ data: 'imageBuffer' });
        imageSize.mockReturnValue({ width: 800, height: 600 });
        uploadImageToGCS.mockResolvedValue('https://gcs.com/image.jpg');

        const result = await processTweetImages([imageUrl], dummyArticleId);
        expect(result).toEqual(['https://gcs.com/image.jpg']);
        expect(uploadImageToGCS).toHaveBeenCalledWith(imageUrl, dummyArticleId);
    });

    it('Tweet has no image', async () => {
        const result = await processTweetImages([], dummyArticleId);
        expect(result).toEqual([]);
        expect(uploadImageToGCS).not.toHaveBeenCalled();
    });

    it('Image type is unsupported (e.g., .svg)', async () => {
        const imageUrl = 'https://image.com/image.svg';
        const result = await processTweetImages([imageUrl], dummyArticleId);
        expect(result).toEqual([]);
        expect(uploadImageToGCS).not.toHaveBeenCalled();
    });

    it('Multiple images but all are irrelevant', async () => {
        const imageUrls = ['https://img1.jpg', 'https://img2.jpg'];
        axios.get.mockResolvedValue({ data: 'buffer' });
        imageSize.mockReturnValue({ width: 5, height: 5 }); // Too small

        const result = await processTweetImages(imageUrls, dummyArticleId);
        expect(result).toEqual([]);
        expect(uploadImageToGCS).not.toHaveBeenCalled();
    });

    it('Image has invalid dimensions (1x1 tracking pixel)', async () => {
        const imageUrl = 'https://tracker.com/pixel.jpg';
        axios.get.mockResolvedValue({ data: 'buffer' });
        imageSize.mockReturnValue({ width: 1, height: 1 });

        const result = await processTweetImages([imageUrl], dummyArticleId);
        expect(result).toEqual([]);
        expect(uploadImageToGCS).not.toHaveBeenCalled();
    });
});
