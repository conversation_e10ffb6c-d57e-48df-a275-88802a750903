const {
  normalizeModelResponse,
  convertResponseToSchema
} = require('../utils/modelIntegrator');



let processNewsArticle, checkApiKeysHealth;

beforeEach(() => {
  jest.resetModules();
  jest.clearAllMocks();

  jest.mock("fs");
  jest.mock("path");
  jest.mock("@google/generative-ai");
  jest.mock("../utils/constants", () => ({
    API_KEYS: ["fake-api-key-1", "fake-api-key-2"],
    TEMP_FILE_DIR: "temp_dir",
    PROMPT_TEMPLATE: "Extract vessel info",
    VESSEL_TYPE: [],
    VESSEL_TYPE_GROUP: []
  }));
  jest.mock("../utils/newsDbManager");
  jest.mock("../utils/logger", () => () => ({
    info: jest.fn(), warn: jest.fn(), error: jest.fn(), debug: jest.fn()
  }));

  const fs = require("fs");
  fs.writeFileSync = jest.fn();
  fs.mkdirSync = jest.fn();
  fs.unlinkSync = jest.fn();
  fs.rmdirSync = jest.fn();
  fs.existsSync = jest.fn(() => true);
  fs.readdirSync = jest.fn(() => []);

  const path = require("path");
  path.join = jest.fn((...args) => args.join("/"));

  const mockModel = {
    generateContent: jest.fn()
  };

  const mockGenAI = {
    getGenerativeModel: jest.fn(() => mockModel)
  };

  require("@google/generative-ai").GoogleGenerativeAI.mockImplementation(() => mockGenAI);
  global.mockModel = mockModel;

  const newsDbManager = require("../utils/newsDbManager");
  newsDbManager.getAllOrganisation.mockResolvedValue([{ name: "IMO", id: "o1" }]);
  newsDbManager.getAllCategory.mockResolvedValue([{ name: "Casualty", id: "c1" }]);
  newsDbManager.getAllRegion.mockResolvedValue(["Red Sea"]);

  ({ processNewsArticle, checkApiKeysHealth } = require("../utils/modelIntegrator"));
});

const baseInput = {
  newsTitle: "Explosion in Port",
  newsSummary: "Fire on commercial vessel.",
  articleId: "a1"
};

describe("modelIntegrator full coverage", () => {

  it("should return default response with minimal match", async () => {
    const { generateDefaultResponse } = require("../utils/modelIntegrator");
    const inputValues = {
      news_title: "Explosion reported by IMO",
      news_summary: "Casualty happened near Red Sea",
      ORGANIZATIONS: [{ name: "IMO", id: "org1" }],
      CATEGORIES: [{ name: "Casualty", id: "cat1" }],
      REGIONS: ["Red Sea"]
    };

    const result = generateDefaultResponse(inputValues, "test123");
    const parsed = JSON.parse(result.candidates[0].content.parts[0].text);
    expect(parsed.length).toBeGreaterThan(0);
    expect(parsed[0]).toHaveProperty("organization");
    expect(parsed[0]).toHaveProperty("category");
    expect(parsed[0]).toHaveProperty("region");
  });

  it("should create temp input file and cleanup", () => {
    const { createTemporaryInputFile, cleanupTemporaryFiles } = require("../utils/modelIntegrator");
    const inputValues = { foo: "bar" };
    const { filePath, dirPath } = createTemporaryInputFile(inputValues, "temp123");
    expect(filePath).toContain("temp123");
    expect(dirPath).toContain("temp123");

    cleanupTemporaryFiles(filePath, dirPath, "temp123");
    const fs = require("fs");
    expect(fs.unlinkSync).toHaveBeenCalledWith(filePath);
    expect(fs.rmdirSync).toHaveBeenCalledWith(dirPath);
  });

  it("should return normalized response", async () => {
    global.mockModel.generateContent.mockResolvedValue({
      response: {
        text: () => JSON.stringify({ vessels: [{ vessel_name: "Vessel A" }] })
      }
    });

    const res = await processNewsArticle(baseInput.newsTitle, baseInput.newsSummary, baseInput.articleId);
    expect(res.length).toBeGreaterThanOrEqual(1);
  });

  it("should return empty array when db call fails", async () => {
    const newsDbManager = require("../utils/newsDbManager");
    newsDbManager.getAllOrganisation.mockRejectedValue(new Error("fail"));
    const res = await processNewsArticle("x", "y", "z");
    expect(res).toEqual([]);
  });

  it("should clean up temp files", async () => {
    global.mockModel.generateContent.mockResolvedValue({
      response: {
        text: () => JSON.stringify({ vessels: [{ vessel_name: "Ship B" }] })
      }
    });

    const fs = require("fs");
    const res = await processNewsArticle("A", "B", "cleanup123");
    expect(fs.unlinkSync).toHaveBeenCalled();
    expect(fs.rmdirSync).toHaveBeenCalled();
    expect(res.length).toBeGreaterThanOrEqual(1);
  });

  it("should handle API health check success", async () => {
    global.mockModel.generateContent.mockResolvedValue({
      response: {
        text: () => "API key is working"
      }
    });

    const res = await checkApiKeysHealth();
    expect(res.summary.total).toBeGreaterThan(0);
    expect(res.details.length).toBeGreaterThan(0);
  });

  it("should simulate rate limit in health check", async () => {
    global.mockModel.generateContent.mockImplementation(() => Promise.reject(new Error("Rate limit")));
    const res = await checkApiKeysHealth();
    expect(res.details[0].status).toBe("error");
  });

  it("should fallback with default response for blank model output", async () => {
    global.mockModel.generateContent.mockResolvedValue({
      response: {
        text: () => "[]"
      }
    });

    const res = await processNewsArticle("No match", "None", "blank123");
    expect(Array.isArray(res)).toBe(true);
    expect(res.length).toBeGreaterThanOrEqual(1);
  });

  it("should handle malformed JSON and return fallback", async () => {
    global.mockModel.generateContent.mockResolvedValue({
      response: {
        text: () => "invalid_json"
      }
    });

    const res = await processNewsArticle("bad", "json", "failJSON");
    expect(Array.isArray(res)).toBe(true);
    expect(res.length).toBeGreaterThanOrEqual(1);
  });

  it("should retry on transient error and succeed", async () => {
    global.mockModel.generateContent
      .mockRejectedValueOnce(new Error("temporary"))
      .mockResolvedValueOnce({
        response: {
          text: () => JSON.stringify({ vessels: [{ vessel_name: "Recovered" }] })
        }
      });

    const res = await processNewsArticle("retry", "once", "transient");
    expect(res.length).toBeGreaterThan(0);
  });
});


const { convertToSchema } = require('../utils/responseSchemaConverter');

describe('responseSchemaConverter.js', () => {
  const schema = {
    type: 'object',
    properties: {
      name: { type: 'string', default: 'Unknown' },
      age: { type: 'number' },
      tags: {
        type: 'array',
        items: { type: 'string' }
      }
    }
  };

  it('should convert valid input with strict mode', () => {
    const input = { name: 'John', age: 30, tags: ['a', 'b'] };
    const output = convertToSchema(input, schema, { strictMode: true });
    expect(output).toEqual(input);
  });

  it('should remove extra fields in strict mode', () => {
    const input = { name: 'John', age: 30, extra: 'remove' };
    const output = convertToSchema(input, schema, { strictMode: true, removeExtraFields: true });
    expect(output).not.toHaveProperty('extra');
  });

  it('should fill default values when requested', () => {
    const input = { age: 25 };
    const output = convertToSchema(input, schema, { defaultValues: true });
    expect(output.name).toBe('Unknown');
  });

  it('should handle invalid schema gracefully', () => {
    const badSchema = { type: 'unknown' };
    const result = convertToSchema({ any: true }, badSchema);
    expect(result).toEqual({ any: true }); // fallback behavior
  });

  it('should return null if input is not object/array', () => {
    expect(convertToSchema('invalid', schema)).toBeNull();
  });

  it('should apply default for missing nested object fields', () => {
    const nestedSchema = {
      type: 'object',
      properties: {
        details: {
          type: 'object',
          properties: {
            score: { type: 'number', default: 10 }
          }
        }
      }
    };

    const output = convertToSchema({ details: {} }, nestedSchema, { defaultValues: true });
    expect(output.details.score).toBe(10);
  });

  it('should fallback if extractJsonFromText receives undefined', async () => {
    const { extractJsonFromText } = require('../utils/modelIntegrator');
    const result = extractJsonFromText(undefined);
    expect(result).toBeNull();
  });

  it('should fallback for invalid markdown block JSON', async () => {
    const { extractJsonFromText } = require('../utils/modelIntegrator');
    const invalid = "```json\ninvalid json\n```";
    const result = extractJsonFromText(invalid);
    expect(result).toBeNull();
  });

  it('should return empty array if raw string has invalid JSON', () => {
    const { normalizeModelResponse } = require('../utils/modelIntegrator');
    const result = normalizeModelResponse("```json\ninvalid json\n```", "testArticle");
    expect(result).toEqual([]);
  });

  it('should fallback for non-array JSON response in normalizeModelResponse', () => {
    const { normalizeModelResponse } = require('../utils/modelIntegrator');
    const result = normalizeModelResponse({ invalid: true });
    expect(Array.isArray(result)).toBe(true);
  });

  it('should handle missing vessels in response', () => {
    const res = normalizeModelResponse([{ is_relevant: true }], "testArticle");
    expect(res[0].is_relevant).toBe(true);

    const vessel = { vessel_name: "V1" }; // ✅ Add this line
    const result = convertResponseToSchema(vessel, {});
    expect(result).toEqual({
      vessels: [
        {
          vessel_name: "V1",
          vessel_type: "Unknown",
          vessel_type_group: "Unknown",
          categories: [],
          region: [],
          organizations: []
        }
      ]
    });
  });


  it('should return original vessel if schema is empty', () => {
    const { convertResponseToSchema } = require('../utils/modelIntegrator');
    const vessel = { vessel_name: "V1" };
    const result = convertResponseToSchema(vessel, {});
    expect(result).toEqual({
      vessels: [
        {
          vessel_name: "V1",
          vessel_type: "Unknown",
          vessel_type_group: "Unknown",
          categories: [],
          region: [],
          organizations: []
        }
      ]
    });
  });


});
