const { fetchAndProcessTweets } = require('./twitterProcessor');
const getLogger = require('./logger');

const logger = getLogger();

async function runTwitterFetch() {
  try {
    logger.info('Starting scheduled Twitter fetch');
    await fetchAndProcessTweets();
  } catch (error) {
    logger.error(`Error in Twitter fetch: ${error.message}`);
    // Don't throw error, keep scheduler running
  }
}

module.exports = {
  runTwitterFetch
};
