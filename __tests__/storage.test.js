// __tests__/storage.test.js

const fs = require('fs');
const path = require('path');
const axios = require('axios');
const { uploadImageToGCS } = require('../utils/storage');

jest.mock('axios');
jest.mock('fs');
jest.mock('path');

// ✅ Mock constants
jest.mock('../utils/constants', () => ({
  BUCKET_NAME: 'test-bucket',
  BUCKET_FOLDER: 'test-folder',
  CDN_URL: 'https://mock.cdn.com',
}));

// ✅ Mock Google Cloud Storage
jest.mock('@google-cloud/storage', () => {
  const mFile = {
    save: jest.fn((buffer, options, callback) => {
      callback(null); // simulate success
    }),
    publicUrl: jest.fn(() => 'https://mock.cdn.com/test-folder/testfile.jpg'),
  };
  const mBucket = {
    file: jest.fn(() => mFile),
  };
  const mStorage = {
    bucket: jest.fn(() => mBucket),
  };
  return { Storage: jest.fn(() => mStorage) };
});

// ✅ Setup fs and path mocks
fs.promises = {
  writeFile: jest.fn().mockResolvedValue(undefined),
  unlink: jest.fn().mockResolvedValue(undefined),
};
path.basename = jest.fn(() => 'testfile.jpg');

// ✅ Test starts here
describe('uploadImageToGCS', () => {
  const dummyUrl = 'https://example.com/image.jpg';
  const dummyId = 'article-id-123';

  beforeEach(() => {
    jest.clearAllMocks();
    axios.get.mockResolvedValue({ data: Buffer.from('fake-image-data') });
  });

  it('should return null if axios fails', async () => {
    axios.get.mockRejectedValue(new Error('Download failed'));
    const result = await uploadImageToGCS(dummyUrl, dummyId);
    expect(result).toBeNull();
  });

  it('should return null if save throws error', async () => {
    const { Storage } = require('@google-cloud/storage');
    const storageInstance = new Storage();
    const file = storageInstance.bucket().file();
    file.save.mockImplementationOnce((buffer, options, callback) => {
      callback(new Error('Upload failed'));
    });

    const result = await uploadImageToGCS(dummyUrl, dummyId);
    expect(result).toBeNull();
  });
});
