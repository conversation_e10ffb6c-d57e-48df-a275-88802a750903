// FIX: Define mockLogger before all mocks
const mockLogger = {
  error: jest.fn(),
  info: jest.fn()
};

jest.mock('../utils/logger', () => () => mockLogger);

jest.mock('../utils/databaseConfig', () => ({
  getNewsCentreConnection: jest.fn(),
  releaseNewsCentreConnection: jest.fn()
}));

jest.mock('../utils/constants', () => ({
  INSERT_OR_UPDATE_MUTED_COMBO: 'INSERT QUERY MOCK',
  GET_MUTED_VESSELS_QUERY: 'SELECT vessel_name FROM muted_combos WHERE count > 10',
  RESET_EXPIRED_MUTED_COMBOS: 'UPDATE muted_combos SET count = 0 WHERE updated_at < NOW() - interval \'24 hours\''
}));

const {
  getMutedVessels,
  insertOrUpdateMutedCombo,
  resetExpiredMutedCombos
} = require('../utils/mutedComboManager');
const {
  getNewsCentreConnection,
  releaseNewsCentreConnection
} = require('../utils/databaseConfig');

describe('mutedComboManager', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return list of vessel names from DB', async () => {
    const mockQuery = jest.fn().mockResolvedValue({
      rows: [{ vessel_name: 'Titanic' }, { vessel_name: 'Evergreen' }]
    });
    const mockClient = { query: mockQuery };
    getNewsCentreConnection.mockResolvedValue(mockClient);

    const result = await getMutedVessels();

    expect(result).toEqual(['Titanic', 'Evergreen']);
    expect(mockQuery).toHaveBeenCalledWith(expect.any(String));
    expect(releaseNewsCentreConnection).toHaveBeenCalledWith(mockClient);
  });

  it('should insert or update combo and return count', async () => {
    const mockClient = {
      query: jest.fn().mockResolvedValue({ rows: [{ count: 12 }] })
    };
    getNewsCentreConnection.mockResolvedValue(mockClient);

    const count = await insertOrUpdateMutedCombo('ShipX', 'explosion', 'fire');

    expect(count).toBe(12);
    expect(mockClient.query).toHaveBeenCalledWith('INSERT QUERY MOCK', ['ShipX', 'explosion', 'fire']);
    expect(releaseNewsCentreConnection).toHaveBeenCalledWith(mockClient);
  });

  it('should reset expired muted combos', async () => {
    const mockQuery = jest.fn().mockResolvedValue();
    const mockClient = { query: mockQuery };
    getNewsCentreConnection.mockResolvedValue(mockClient);

    await resetExpiredMutedCombos();

    expect(mockQuery).toHaveBeenCalledWith(expect.any(String));
    expect(releaseNewsCentreConnection).toHaveBeenCalledWith(mockClient);
  });

  it('should handle and log error in insertOrUpdateMutedCombo', async () => {
    const mockClient = { query: jest.fn().mockRejectedValue(new Error('Insert failed')) };
    getNewsCentreConnection.mockResolvedValue(mockClient);

    await expect(
      insertOrUpdateMutedCombo('ShipX', 'blast', 'leak')
    ).rejects.toThrow('Insert failed');

    expect(mockLogger.error).toHaveBeenCalledWith(
      expect.stringContaining('insertOrUpdateMutedCombo')
    );
    expect(releaseNewsCentreConnection).toHaveBeenCalledWith(mockClient);
  });

  it('should handle and log error in getMutedVessels', async () => {
    const mockClient = { query: jest.fn().mockRejectedValue(new Error('Fetch failed')) };
    getNewsCentreConnection.mockResolvedValue(mockClient);

    await expect(getMutedVessels()).rejects.toThrow('Fetch failed');
    expect(mockLogger.error).toHaveBeenCalledWith(
      expect.stringContaining('fetching muted vessels')
    );
    expect(releaseNewsCentreConnection).toHaveBeenCalledWith(mockClient);
  });

  it('should handle and log error in resetExpiredMutedCombos', async () => {
    const mockClient = { query: jest.fn().mockRejectedValue(new Error('Reset failed')) };
    getNewsCentreConnection.mockResolvedValue(mockClient);

    await expect(resetExpiredMutedCombos()).rejects.toThrow('Reset failed');
    expect(mockLogger.error).toHaveBeenCalledWith(
      expect.stringContaining('resetting expired muted combos')
    );
    expect(releaseNewsCentreConnection).toHaveBeenCalledWith(mockClient);
  });

  // 🔁 Branch Coverage Cases — No client assigned

  it('should skip release when insertOrUpdateMutedCombo fails before client init', async () => {
    getNewsCentreConnection.mockRejectedValue(new Error('Conn error'));
    await expect(
      insertOrUpdateMutedCombo('V', 'a', 'b')
    ).rejects.toThrow('Conn error');
    expect(releaseNewsCentreConnection).not.toHaveBeenCalled();
  });

  it('should skip release when getMutedVessels fails before client init', async () => {
    getNewsCentreConnection.mockRejectedValue(new Error('Vessel init fail'));
    await expect(getMutedVessels()).rejects.toThrow('Vessel init fail');
    expect(releaseNewsCentreConnection).not.toHaveBeenCalled();
  });

  it('should skip release when resetExpiredMutedCombos fails before client init', async () => {
    getNewsCentreConnection.mockRejectedValue(new Error('Reset init fail'));
    await expect(resetExpiredMutedCombos()).rejects.toThrow('Reset init fail');
    expect(releaseNewsCentreConnection).not.toHaveBeenCalled();
  });
});
