jest.resetModules(); // Clear previous modules

// Define mock client and pool
const mockClient = {
    release: jest.fn(),
    query: jest.fn()
};

const mockPool = {
    connect: jest.fn(() => Promise.resolve(mockClient)),
    on: jest.fn((event, callback) => {
        // Manually store the callback to simulate event later
        if (event === 'connect') {
            callback(mockClient);
        } else if (event === 'error') {
            mockPool.__errorHandler__ = callback;
        }
    }),
    __errorHandler__: null,
    end: jest.fn(() => Promise.resolve())
};

// Mock pg before importing actual implementation
jest.mock('pg', () => ({
    Pool: jest.fn(() => mockPool)
}));

describe('databaseConfig.js coverage', () => {
    let getNewsCentreConnection, releaseNewsCentreConnection, closeNewsCentrePool;

    beforeEach(() => {
        jest.clearAllMocks();
        jest.resetModules();

        const db = require('../utils/databaseConfig');
        getNewsCentreConnection = db.getNewsCentreConnection;
        releaseNewsCentreConnection = db.releaseNewsCentreConnection;
        closeNewsCentrePool = db.closeNewsCentrePool;
    });

    it('should successfully get a database connection', async () => {
        const client = await getNewsCentreConnection();
        expect(client).toBeDefined();
        expect(mockPool.connect).toHaveBeenCalled();
    });

    it('should release the connection successfully', async () => {
        const client = await getNewsCentreConnection();
        releaseNewsCentreConnection(client);
        expect(mockClient.release).toHaveBeenCalled();
    });

    it('should not throw error if release is called with undefined', () => {
        expect(() => releaseNewsCentreConnection(undefined)).not.toThrow();
    });

    it('should throw an error if connection fails', async () => {
        mockPool.connect.mockRejectedValueOnce(new Error('Connection error'));
        await expect(getNewsCentreConnection()).rejects.toThrow('Connection error');
    });

    it('should throw if release is not a function', () => {
        const badClient = { release: 'notAFunction' };
        expect(() => releaseNewsCentreConnection(badClient)).toThrow('client.release is not a function');
    });

    it('should catch and throw if client.release itself throws', () => {
        const throwingClient = { release: () => { throw new Error('Release failed'); } };
        expect(() => releaseNewsCentreConnection(throwingClient)).toThrow('Release failed');
    });

    it('should simulate idle pool error', async () => {
        await getNewsCentreConnection();
        const error = new Error('Idle error');
        if (mockPool.__errorHandler__) {
            expect(() => mockPool.__errorHandler__(error)).not.toThrow();
        }
    });

    it('should close the connection pool successfully', async () => {
        await getNewsCentreConnection(); // initialize the pool
        await expect(closeNewsCentrePool()).resolves.not.toThrow();
        expect(mockPool.end).toHaveBeenCalled();
    });

    it('should throw error while closing the pool', async () => {
        mockPool.end = jest.fn(() => Promise.reject(new Error('Close failed')));
        await getNewsCentreConnection();
        await expect(closeNewsCentrePool()).rejects.toThrow('Close failed');
    });

    it('should throw error if pool is null or broken', async () => {
        jest.resetModules();
        jest.doMock('pg', () => ({ Pool: jest.fn(() => null) }));
        const db = require('../utils/databaseConfig');
        await expect(db.getNewsCentreConnection()).rejects.toThrow();
    });

    it('should not throw if client is null', () => {
        expect(() => releaseNewsCentreConnection(null)).not.toThrow();
    });
});
