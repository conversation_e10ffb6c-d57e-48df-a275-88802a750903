const mockLogger = {
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn()
};

const mockFs = {
  writeFileSync: jest.fn(),
  mkdirSync: jest.fn(),
  unlinkSync: jest.fn(),
  rmdirSync: jest.fn(),
  existsSync: jest.fn(() => true),
  readdirSync: jest.fn(() => [])
};

const mockPath = {
  join: jest.fn((...args) => args.join("/"))
};

const mockModel = {
  generateContent: jest.fn()
};

const mockGenAI = {
  getGenerativeModel: jest.fn(() => mockModel)
};

const mockNewsDbManager = {
  getAllOrganisation: jest.fn(),
  getAllCategory: jest.fn(),
  getAllRegion: jest.fn()
};

jest.mock("fs", () => mockFs);
jest.mock("path", () => mockPath);
jest.mock("../utils/logger", () => () => mockLogger);
jest.mock("@google/generative-ai", () => ({
  GoogleGenerativeAI: jest.fn(() => mockGenAI)
}));
jest.mock("../utils/newsDbManager", () => mockNewsDbManager);
jest.mock("../utils/constants", () => ({
  API_KEYS: ["fake-api-key-1", "fake-api-key-2", "fake-api-key-3"],
  TEMP_FILE_DIR: "temp_dir",
  PROMPT_TEMPLATE: "Extract vessel info from: {input}",
  VESSEL_TYPE: ["Container Ship", "Tanker"],
  VESSEL_TYPE_GROUP: ["Commercial", "Military"]
}));


const {
  processNewsArticle,
  checkApiKeysHealth,
  extractJsonFromText,
  normalizeModelResponse,
  convertResponseToSchema,
  createTemporaryInputFile,
  cleanupTemporaryFiles,
  generateDefaultResponse
} = require('../utils/modelIntegrator');

describe("modelIntegrator.js - Comprehensive Coverage Tests", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockNewsDbManager.getAllOrganisation.mockResolvedValue([
      { name: "IMO", id: "org1" },
      { name: "Coast Guard", id: "org2" }
    ]);
    mockNewsDbManager.getAllCategory.mockResolvedValue([
      { name: "Casualty", id: "cat1" },
      { name: "Collision", id: "cat2" }
    ]);
    mockNewsDbManager.getAllRegion.mockResolvedValue(["Red Sea", "Mediterranean"]);

    mockModel.generateContent.mockResolvedValue({
      response: {
        text: () => JSON.stringify({
          is_relevant: true,
          vessels: [{ vessel_name: "Test Ship", vessel_type: "Container Ship" }]
        })
      }
    });
  });

  describe("extractJsonFromText", () => {
    it("should extract JSON from object match", () => {
      const text = 'Some text {"vessel_name": "Ship A"} more text';
      const result = extractJsonFromText(text, "test1");
      expect(result).toEqual({ vessel_name: "Ship A" });
    });

    it("should extract JSON from array match when no object present", () => {
      const text = 'Some text [{"vessel_name": "Ship A"}] more text';
      const result = extractJsonFromText(text, "test2");

      expect(result).toEqual({ vessel_name: "Ship A" });
    });

    it("should extract JSON from pure array without object pattern", () => {
      const text = 'Some text [1, 2, 3] more text';
      const result = extractJsonFromText(text, "test2b");
      expect(result).toEqual([1, 2, 3]);
    });

    it("should extract JSON from code block", () => {
      const text = '```json\n{"vessel_name": "Ship A"}\n```';
      const result = extractJsonFromText(text, "test3");
      expect(result).toEqual({ vessel_name: "Ship A" });
    });

    it("should return null for invalid JSON", () => {
      const text = 'invalid json content';
      const result = extractJsonFromText(text, "test4");
      expect(result).toBeNull();
    });

    it("should handle undefined input", () => {
      const result = extractJsonFromText(undefined, "test5");
      expect(result).toBeNull();
    });

    it("should handle JSON parsing errors gracefully", () => {
      const text = '{"invalid": json}';
      const result = extractJsonFromText(text, "test6");
      expect(result).toBeNull();
      expect(mockLogger.error).toHaveBeenCalled();
    });

    it("should prioritize object match over array match", () => {
      const text = 'Object: {"vessel_name": "Ship A"} Array: [1, 2, 3]';
      const result = extractJsonFromText(text, "test7");
      expect(result).toEqual({ vessel_name: "Ship A" });
    });
  });

  describe("normalizeModelResponse", () => {
    it("should handle string response with valid JSON", () => {
      const response = '{"vessels": [{"vessel_name": "Ship A"}]}';
      const result = normalizeModelResponse(response, "test1");
      expect(result).toEqual([{ vessels: [{ vessel_name: "Ship A" }] }]);
    });

    it("should handle Gemini API response format", () => {
      const response = {
        candidates: [{
          content: {
            parts: [{ text: '[{"vessel_name": "Ship B"}]' }]
          }
        }]
      };
      const result = normalizeModelResponse(response, "test2");
      expect(result).toEqual([{ vessel_name: "Ship B" }]);
    });

    it("should handle object with numeric keys", () => {
      const response = { "0": { vessel_name: "Ship C" }, "1": { vessel_name: "Ship D" } };
      const result = normalizeModelResponse(response, "test3");
      expect(result).toEqual([{ vessel_name: "Ship C" }, { vessel_name: "Ship D" }]);
    });

    it("should handle null/undefined response", () => {
      expect(normalizeModelResponse(null, "test4")).toEqual([]);
      expect(normalizeModelResponse(undefined, "test5")).toEqual([]);
    });

    it("should handle array response", () => {
      const response = [{ vessel_name: "Ship E" }];
      const result = normalizeModelResponse(response, "test6");
      expect(result).toEqual([{ vessel_name: "Ship E" }]);
    });

    it("should handle single object response", () => {
      const response = { vessel_name: "Ship F" };
      const result = normalizeModelResponse(response, "test7");
      expect(result).toEqual([{ vessel_name: "Ship F" }]);
    });

    it("should handle invalid string response", () => {
      const response = "invalid json string";
      const result = normalizeModelResponse(response, "test8");
      expect(result).toEqual([]);
    });

    it("should handle errors gracefully", () => {

      const invalidResponse = {
        candidates: [{
          content: {
            parts: [{ text: "invalid json that will cause error" }]
          }
        }]
      };

      const result = normalizeModelResponse(invalidResponse, "test9");
      expect(result).toEqual([]);
      expect(mockLogger.warn).toHaveBeenCalledWith(
        expect.stringContaining("Failed to extract JSON from response")
      );
    });
  });

  describe("File Operations", () => {
    it("should create temporary input file", () => {
      const inputValues = { test: "data" };
      const result = createTemporaryInputFile(inputValues, "test123");

      expect(result.filePath).toContain("test123");
      expect(result.dirPath).toContain("test123");
      expect(mockFs.mkdirSync).toHaveBeenCalled();
      expect(mockFs.writeFileSync).toHaveBeenCalled();
      expect(mockLogger.info).toHaveBeenCalledWith(
        expect.stringContaining("Created temporary input file")
      );
    });

    it("should cleanup temporary files successfully", () => {
      const filePath = "/test/file.json";
      const dirPath = "/test";

      cleanupTemporaryFiles(filePath, dirPath, "test123");

      expect(mockFs.unlinkSync).toHaveBeenCalledWith(filePath);
      expect(mockFs.rmdirSync).toHaveBeenCalledWith(dirPath);
      expect(mockLogger.info).toHaveBeenCalledWith(
        expect.stringContaining("Deleted temporary file")
      );
    });

    it("should handle cleanup errors gracefully", () => {
      mockFs.unlinkSync.mockImplementationOnce(() => {
        throw new Error("File deletion failed");
      });

      cleanupTemporaryFiles("/test/file.json", "/test", "test123");

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining("Error cleaning up temporary files"),
        expect.any(Error)
      );
    });

    it("should skip cleanup when directory is not empty", () => {
      mockFs.readdirSync.mockReturnValueOnce(["other-file.txt"]);

      cleanupTemporaryFiles("/test/file.json", "/test", "test123");

      expect(mockFs.unlinkSync).toHaveBeenCalled();
      expect(mockFs.rmdirSync).not.toHaveBeenCalled();
    });

    it("should handle non-existent files in cleanup", () => {
      mockFs.existsSync.mockReturnValue(false);

      cleanupTemporaryFiles("/test/file.json", "/test", "test123");

      expect(mockFs.unlinkSync).not.toHaveBeenCalled();
      expect(mockFs.rmdirSync).not.toHaveBeenCalled();
    });
  });

  describe("generateDefaultResponse", () => {
    it("should generate default response with matching keywords", () => {
      const inputValues = {
        news_title: "IMO reports casualty in Red Sea",
        news_summary: "Coast Guard responds to collision",
        ORGANIZATIONS: [
          { name: "IMO", id: "org1" },
          { name: "Coast Guard", id: "org2" }
        ],
        CATEGORIES: [
          { name: "Casualty", id: "cat1" },
          { name: "Collision", id: "cat2" }
        ],
        REGIONS: ["Red Sea", "Mediterranean"]
      };

      const result = generateDefaultResponse(inputValues, "test123");
      const parsed = JSON.parse(result.candidates[0].content.parts[0].text);

      expect(parsed.length).toBeGreaterThan(0);
      expect(parsed[0]).toHaveProperty("organization");
      expect(parsed[0]).toHaveProperty("category");
      expect(parsed[0]).toHaveProperty("region");
      expect(parsed[0]).toHaveProperty("confidence");
    });

    it("should handle empty input values", () => {
      const inputValues = {
        news_title: "",
        news_summary: "",
        ORGANIZATIONS: [],
        CATEGORIES: [],
        REGIONS: []
      };

      const result = generateDefaultResponse(inputValues, "test124");
      const parsed = JSON.parse(result.candidates[0].content.parts[0].text);

      expect(parsed.length).toBeGreaterThan(0);
      expect(parsed[0].organization).toBeNull();
      expect(parsed[0].category).toBeNull();
      expect(parsed[0].region).toBeNull();
    });

    it("should handle multiple matches and create additional responses", () => {
      const inputValues = {
        news_title: "IMO and Coast Guard report casualty and collision in Red Sea and Mediterranean",
        news_summary: "Multiple incidents reported",
        ORGANIZATIONS: [
          { name: "IMO", id: "org1" },
          { name: "Coast Guard", id: "org2" }
        ],
        CATEGORIES: [
          { name: "Casualty", id: "cat1" },
          { name: "Collision", id: "cat2" }
        ],
        REGIONS: ["Red Sea", "Mediterranean"]
      };

      const result = generateDefaultResponse(inputValues, "test125");
      const parsed = JSON.parse(result.candidates[0].content.parts[0].text);

      expect(parsed.length).toBeGreaterThan(1);
    });

    it("should handle errors in default response generation", () => {
      const inputValues = {
        news_title: "Test",
        news_summary: "Test",
        ORGANIZATIONS: [{ name: null, id: "org1" }],
        CATEGORIES: [],
        REGIONS: []
      };

      const result = generateDefaultResponse(inputValues, "test126");
      expect(result.candidates[0].content.parts[0].text).toBeDefined();
    });

    it("should handle error in JSON.stringify and return minimal response", () => {

      const originalStringify = JSON.stringify;
      JSON.stringify = jest.fn(() => { throw new Error("Stringify error"); });

      const inputValues = {
        news_title: "Test",
        news_summary: "Test",
        ORGANIZATIONS: [],
        CATEGORIES: [],
        REGIONS: []
      };

      const result = generateDefaultResponse(inputValues, "test127");
      expect(result.candidates[0].content.parts[0].text).toBe("[]");
      expect(mockLogger.error).toHaveBeenCalled();


      JSON.stringify = originalStringify;
    });
  });

  describe("processNewsArticle", () => {
    it("should process article successfully with valid response", async () => {
      mockModel.generateContent.mockResolvedValue({
        response: {
          text: () => JSON.stringify({
            is_relevant: true,
            vessels: [{ vessel_name: "Test Ship", vessel_type: "Container Ship" }]
          })
        }
      });

      const result = await processNewsArticle("Test Title", "Test Summary", "test123");

      expect(result.length).toBeGreaterThan(0);
      expect(mockLogger.info).toHaveBeenCalledWith(
        expect.stringContaining("Step 1: Gathering data from database")
      );
      expect(mockNewsDbManager.getAllOrganisation).toHaveBeenCalled();
      expect(mockNewsDbManager.getAllCategory).toHaveBeenCalled();
      expect(mockNewsDbManager.getAllRegion).toHaveBeenCalled();
    });

    it("should return empty array when database calls fail", async () => {
      mockNewsDbManager.getAllOrganisation.mockRejectedValue(new Error("DB Error"));

      const result = await processNewsArticle("Test Title", "Test Summary", "test124");

      expect(result).toEqual([]);
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining("Error processing news article"),
        expect.any(Error)
      );
    });



    it("should cleanup temporary files even when errors occur", async () => {
      mockNewsDbManager.getAllOrganisation.mockRejectedValue(new Error("DB Error"));

      await processNewsArticle("Test Title", "Test Summary", "test126");

      expect(mockLogger.info).toHaveBeenCalledWith(
        expect.stringContaining("Cleaning up temporary files")
      );
    });

    it("should handle empty model response", async () => {
      mockModel.generateContent.mockResolvedValue({
        response: {
          text: () => "[]"
        }
      });

      const result = await processNewsArticle("Test Title", "Test Summary", "test127");

      expect(Array.isArray(result)).toBe(true);

      expect(mockLogger.warn).toHaveBeenCalledWith(
        expect.stringContaining("Schema conversion returned empty or invalid result")
      );
    });

    it("should handle malformed JSON response", async () => {
      mockModel.generateContent.mockResolvedValue({
        response: {
          text: () => "invalid json"
        }
      });

      const result = await processNewsArticle("Test Title", "Test Summary", "test128");

      expect(Array.isArray(result)).toBe(true);
    });
  });

  describe("checkApiKeysHealth", () => {
    it("should check all API keys and return operational status", async () => {
      mockModel.generateContent.mockResolvedValue({
        response: {
          text: () => "API key is working"
        }
      });

      const result = await checkApiKeysHealth();

      expect(result.summary.total).toBe(3);
      expect(result.summary.operational).toBeGreaterThan(0);
      expect(result.details).toHaveLength(3);
      expect(result.details[0]).toHaveProperty("keyIndex");
      expect(result.details[0]).toHaveProperty("status");
      expect(result.details[0]).toHaveProperty("responseTime");
      expect(result.timestamp).toBeDefined();
    });

    it("should handle rate limit errors", async () => {
      mockModel.generateContent.mockRejectedValue(new Error("Rate limit exceeded"));

      const result = await checkApiKeysHealth();

      expect(result.details[0].status).toBe("error");
      expect(result.details[0].rateLimited).toBe(true);
      expect(result.summary.error).toBeGreaterThan(0);
      expect(mockLogger.error).toHaveBeenCalled();
    });

    it("should handle timeout errors", async () => {
      mockModel.generateContent.mockRejectedValue(new Error("Request timeout"));

      const result = await checkApiKeysHealth();

      expect(result.details[0].status).toBe("error");
      expect(result.details[0].error).toBe("Request timeout");
    });

    it("should handle degraded responses", async () => {
      mockModel.generateContent.mockResolvedValue({
        response: {
          text: () => "Unexpected response content"
        }
      });

      const result = await checkApiKeysHealth();

      expect(result.details[0].status).toBe("degraded");
      expect(result.details[0].error).toBe("Unexpected response");
    });

    it("should handle quota exceeded errors", async () => {
      mockModel.generateContent.mockRejectedValue(new Error("quota exceeded"));

      const result = await checkApiKeysHealth();

      expect(result.details[0].rateLimited).toBe(true);
      expect(result.summary.rateLimited).toBeGreaterThan(0);
    });

    it("should handle resource exhausted errors", async () => {
      mockModel.generateContent.mockRejectedValue(new Error("resource exhausted"));

      const result = await checkApiKeysHealth();

      expect(result.details[0].rateLimited).toBe(true);
    });

    it("should handle 429 status errors", async () => {
      mockModel.generateContent.mockRejectedValue(new Error("429 Too Many Requests"));

      const result = await checkApiKeysHealth();

      expect(result.details[0].rateLimited).toBe(true);
    });

    it("should add delay between API key tests", async () => {
      const startTime = Date.now();

      mockModel.generateContent.mockResolvedValue({
        response: {
          text: () => "API key is working"
        }
      });

      await checkApiKeysHealth();

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should take at least 2 seconds for 3 keys (1 second delay between each)
      expect(duration).toBeGreaterThan(2000);
    });
  });

  describe("convertResponseToSchema", () => {
    it("should convert response using schema successfully", () => {
      const mockResponse = {
        is_relevant: true,
        vessels: [{ vessel_name: "Test Ship", vessel_type: "Container Ship" }]
      };

      const result = convertResponseToSchema(mockResponse, "test123");

      expect(result).toHaveProperty("vessels");
      expect(result.vessels).toHaveLength(1);
      expect(result.vessels[0]).toHaveProperty("vessel_name");
    });

    it("should handle empty or invalid schema conversion", () => {
      const mockResponse = null;

      const result = convertResponseToSchema(mockResponse, "test124");

      expect(result).toEqual({ vessels: [] });
      expect(mockLogger.warn).toHaveBeenCalledWith(
        expect.stringContaining("Schema conversion returned empty or invalid result")
      );
    });

    it("should fallback to normalization when schema conversion fails", () => {
      const mockResponse = { vessel_name: "Test Ship" };

      const result = convertResponseToSchema(mockResponse, "test125");

      expect(result).toHaveProperty("vessels");
      expect(result.vessels[0]).toHaveProperty("vessel_name", "Test Ship");
      expect(result.vessels[0]).toHaveProperty("vessel_type", "Unknown");
    });

    it("should handle errors in schema conversion", () => {
      // Test with null input which will trigger the fallback path
      const result = convertResponseToSchema(null, "test126");

      expect(result).toEqual({ vessels: [] });
      expect(mockLogger.warn).toHaveBeenCalledWith(
        expect.stringContaining("Schema conversion returned empty or invalid result")
      );
    });
  });

  describe("Edge Cases and Error Handling", () => {
    it("should handle complex nested JSON extraction", () => {
      const complexText = `
        Here's the analysis:
        \`\`\`json
        {
          "is_relevant": true,
          "vessels": [
            {
              "vessel_name": "Complex Ship",
              "categories": [{"name": "Casualty", "type": "Incident"}]
            }
          ]
        }
        \`\`\`
        End of analysis.
      `;

      const result = extractJsonFromText(complexText, "complex1");
      expect(result).toHaveProperty("is_relevant", true);
      expect(result.vessels[0]).toHaveProperty("vessel_name", "Complex Ship");
    });

    it("should handle very large responses efficiently", () => {
      const largeArray = Array(1000).fill().map((_, i) => ({ vessel_name: `Ship ${i}` }));
      const result = normalizeModelResponse(largeArray, "large1");

      expect(result).toHaveLength(1000);
      expect(result[0]).toHaveProperty("vessel_name", "Ship 0");
      expect(result[999]).toHaveProperty("vessel_name", "Ship 999");
    });

    it("should handle mixed data types in responses", () => {
      const mixedResponse = {
        vessels: [
          { vessel_name: "Ship A", confidence: 0.9 },
          { vessel_name: "Ship B", confidence: "high" },
          { vessel_name: null, confidence: undefined }
        ]
      };

      const result = normalizeModelResponse(mixedResponse, "mixed1");
      expect(result).toHaveLength(1);
      expect(result[0].vessels).toHaveLength(3);
    });
  });
});

// Additional tests for responseSchemaConverter integration
describe('responseSchemaConverter Integration', () => {
  const { convertToSchema } = require('../utils/responseSchemaConverter');

  const schema = {
    type: 'object',
    properties: {
      name: { type: 'string', default: 'Unknown' },
      age: { type: 'number' },
      tags: {
        type: 'array',
        items: { type: 'string' }
      }
    }
  };

  it('should convert valid input with strict mode', () => {
    const input = { name: 'John', age: 30, tags: ['a', 'b'] };
    const output = convertToSchema(input, schema, { strictMode: true });
    expect(output).toEqual(input);
  });

  it('should remove extra fields in strict mode', () => {
    const input = { name: 'John', age: 30, extra: 'remove' };
    const output = convertToSchema(input, schema, { strictMode: true, removeExtraFields: true });
    expect(output).not.toHaveProperty('extra');
  });

  it('should fill default values when requested', () => {
    const input = { age: 25 };
    const output = convertToSchema(input, schema, { defaultValues: true });
    expect(output.name).toBe('Unknown');
  });

  it('should handle invalid schema gracefully', () => {
    const badSchema = { type: 'unknown' };
    const result = convertToSchema({ any: true }, badSchema);
    expect(result).toEqual({ any: true });
  });

  it('should return null if input is not object/array', () => {
    expect(convertToSchema('invalid', schema)).toBeNull();
  });

  it('should apply default for missing nested object fields', () => {
    const nestedSchema = {
      type: 'object',
      properties: {
        details: {
          type: 'object',
          properties: {
            score: { type: 'number', default: 10 }
          }
        }
      }
    };

    const output = convertToSchema({ details: {} }, nestedSchema, { defaultValues: true });
    expect(output.details.score).toBe(10);
  });

  it('should handle missing vessels in response', () => {
    const res = normalizeModelResponse([{ is_relevant: true }], "testArticle");
    expect(res[0].is_relevant).toBe(true);

    const vessel = { vessel_name: "V1" };
    const result = convertResponseToSchema(vessel, "test");
    expect(result).toHaveProperty("vessels");
  });
});

// Additional comprehensive tests to achieve 100% coverage
describe('Targeted Coverage Tests for Uncovered Lines', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should test extractJsonFromText with code block without json marker', () => {
    const text = '```\n{"vessel_name": "Ship A"}\n```';
    const result = extractJsonFromText(text, "test-code-block");
    expect(result).toEqual({ vessel_name: "Ship A" });
  });

  it('should handle convertResponseToSchema error in catch block', () => {
    // Mock convertToSchema to throw an error
    const originalConvertToSchema = require('../utils/responseSchemaConverter').convertToSchema;
    require('../utils/responseSchemaConverter').convertToSchema = jest.fn(() => {
      throw new Error("Schema conversion error");
    });

    const result = convertResponseToSchema({ test: "data" }, "error-test");

    expect(result.vessels).toBeDefined();
    // The error is caught and handled, but the function still returns a default response
    // so we just check that the result is valid
    expect(Array.isArray(result.vessels)).toBe(true);

    // Restore original function
    require('../utils/responseSchemaConverter').convertToSchema = originalConvertToSchema;
  });

  it('should handle normalizeModelResponse with unhandled response type', () => {
    // Test with a function as response (unhandled type)
    const result = normalizeModelResponse(() => {}, "unhandled-type-test");

    expect(result).toEqual([]);
    expect(mockLogger.warn).toHaveBeenCalledWith(
      expect.stringContaining("Unhandled response type: function")
    );
  });

  it('should handle normalizeModelResponse error in catch block', () => {
    // Create a response that will cause an error in the processing
    const problematicResponse = {
      candidates: [{
        content: {
          parts: [{
            get text() {
              throw new Error("Text access error");
            }
          }]
        }
      }]
    };

    const result = normalizeModelResponse(problematicResponse, "error-test");

    expect(result).toEqual([]);
    expect(mockLogger.error).toHaveBeenCalledWith(
      expect.stringContaining("Error normalizing response"),
      expect.any(Error)
    );
  });
});

// Final push to 100% coverage - Direct unit tests
describe('Direct Unit Tests for 100% Coverage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should extract JSON from code block without json marker (line 181)', () => {
    const text = '```\n{"vessel_name": "Code Block Ship"}\n```';
    const result = extractJsonFromText(text, "code-block-test");
    expect(result).toEqual({ vessel_name: "Code Block Ship" });
  });

  it('should handle convertResponseToSchema error and return minimal structure (lines 246-250)', () => {
    // Mock convertToSchema to throw an error
    const originalConvertToSchema = require('../utils/responseSchemaConverter').convertToSchema;
    require('../utils/responseSchemaConverter').convertToSchema = jest.fn(() => {
      throw new Error("Schema conversion error");
    });

    const result = convertResponseToSchema({ test: "data" }, "error-test");

    // The function falls back to normalization, so it returns a normalized structure
    expect(result.vessels).toBeDefined();
    expect(Array.isArray(result.vessels)).toBe(true);

    // Restore original function
    require('../utils/responseSchemaConverter').convertToSchema = originalConvertToSchema;
  });

  it('should handle empty response warning (line 761)', async () => {
    // Mock to return empty response that bypasses schema conversion
    mockModel.generateContent.mockResolvedValue({
      response: {
        text: () => "[]"
      }
    });


    const originalConvertToSchema = require('../utils/responseSchemaConverter').convertToSchema;
    require('../utils/responseSchemaConverter').convertToSchema = jest.fn(() => null);

    const result = await processNewsArticle("Test Title", "Test Summary", "empty-warning-test");

    expect(Array.isArray(result)).toBe(true);

    require('../utils/responseSchemaConverter').convertToSchema = originalConvertToSchema;
  });
});


describe('Additional Coverage Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should handle extractJsonFromText with code block without json marker', () => {
    const text = '```\n{"vessel_name": "Ship A"}\n```';
    const result = extractJsonFromText(text, "test-code-block");
    expect(result).toEqual({ vessel_name: "Ship A" });
  });

  it('should handle normalizeModelResponse with unhandled response type', () => {
    const result = normalizeModelResponse(() => {}, "unhandled-type-test");

    expect(result).toEqual([]);
    expect(mockLogger.warn).toHaveBeenCalledWith(
      expect.stringContaining("Unhandled response type: function")
    );
  });

  it('should handle normalizeModelResponse error in catch block', () => {
    const problematicResponse = {
      candidates: [{
        content: {
          parts: [{
            get text() {
              throw new Error("Text access error");
            }
          }]
        }
      }]
    };

    const result = normalizeModelResponse(problematicResponse, "error-test");

    expect(result).toEqual([]);
    expect(mockLogger.error).toHaveBeenCalledWith(
      expect.stringContaining("Error normalizing response"),
      expect.any(Error)
    );
  });
});