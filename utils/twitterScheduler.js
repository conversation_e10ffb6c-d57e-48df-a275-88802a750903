const { fetchAndProcessTweets } = require('./twitterProcessor');
const getLogger = require('./logger');

const logger = getLogger();

async function runTwitterFetch() {
  try {
    logger.info('Starting scheduled Twitter fetch');
    await fetchAndProcessTweets();
  } catch (error) {
    const errorMessage = error && error.message && error.message.trim() ? error.message : 'Unknown error';
    logger.error(`Error in Twitter fetch: ${errorMessage}`);
    // Don't throw error, keep scheduler running
  }
}

module.exports = {
  runTwitterFetch
};
