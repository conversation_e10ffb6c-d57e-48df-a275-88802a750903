// 🧠 MOCKS FIRST - Must be defined before any imports
const mockLogger = {
  info: jest.fn(),
  error: jest.fn()
};

const mockFetchAndProcessTweets = jest.fn();

jest.mock('../utils/logger', () => () => mockLogger);
jest.mock('../utils/twitterProcessor', () => ({
  fetchAndProcessTweets: mockFetchAndProcessTweets
}));

// ✅ IMPORT modules after mocks are applied
const { runTwitterFetch } = require('../utils/twitterScheduler');

describe('twitterScheduler.js', () => {
  beforeEach(() => {
    // Clear all mock calls and reset mock implementations before each test
    jest.clearAllMocks();
  });

  describe('runTwitterFetch', () => {
    it('should successfully fetch and process tweets with proper logging', async () => {
      // Arrange
      mockFetchAndProcessTweets.mockResolvedValueOnce();

      // Act
      await runTwitterFetch();

      // Assert
      expect(mockLogger.info).toHaveBeenCalledTimes(1);
      expect(mockLogger.info).toHaveBeenCalledWith('Starting scheduled Twitter fetch');
      expect(mockFetchAndProcessTweets).toHaveBeenCalledTimes(1);
      expect(mockFetchAndProcessTweets).toHaveBeenCalledWith();
      expect(mockLogger.error).not.toHaveBeenCalled();
    });

    it('should handle errors gracefully without throwing and log error message', async () => {
      // Arrange
      const testError = new Error('Twitter API is down');
      mockFetchAndProcessTweets.mockRejectedValueOnce(testError);

      // Act - Should not throw an error
      await expect(runTwitterFetch()).resolves.toBeUndefined();

      // Assert
      expect(mockLogger.info).toHaveBeenCalledTimes(1);
      expect(mockLogger.info).toHaveBeenCalledWith('Starting scheduled Twitter fetch');
      expect(mockFetchAndProcessTweets).toHaveBeenCalledTimes(1);
      expect(mockFetchAndProcessTweets).toHaveBeenCalledWith();
      expect(mockLogger.error).toHaveBeenCalledTimes(1);
      expect(mockLogger.error).toHaveBeenCalledWith('Error in Twitter fetch: Twitter API is down');
    });

    it('should handle errors with undefined message gracefully', async () => {
      // Arrange
      const testError = new Error();
      testError.message = undefined;
      mockFetchAndProcessTweets.mockRejectedValueOnce(testError);

      // Act
      await runTwitterFetch();

      // Assert
      expect(mockLogger.info).toHaveBeenCalledWith('Starting scheduled Twitter fetch');
      expect(mockFetchAndProcessTweets).toHaveBeenCalledTimes(1);
      expect(mockLogger.error).toHaveBeenCalledTimes(1);
      expect(mockLogger.error).toHaveBeenCalledWith('Error in Twitter fetch: Unknown error');
    });

    it('should handle non-Error objects thrown by fetchAndProcessTweets', async () => {
      // Arrange
      const testError = 'String error message';
      mockFetchAndProcessTweets.mockRejectedValueOnce(testError);

      // Act
      await runTwitterFetch();

      // Assert
      expect(mockLogger.info).toHaveBeenCalledWith('Starting scheduled Twitter fetch');
      expect(mockFetchAndProcessTweets).toHaveBeenCalledTimes(1);
      expect(mockLogger.error).toHaveBeenCalledTimes(1);
      // When a non-Error object is thrown, error.message will be undefined
      expect(mockLogger.error).toHaveBeenCalledWith('Error in Twitter fetch: Unknown error');
    });

    it('should handle null/undefined errors', async () => {
      // Arrange
      mockFetchAndProcessTweets.mockRejectedValueOnce(null);

      // Act
      await runTwitterFetch();

      // Assert
      expect(mockLogger.info).toHaveBeenCalledWith('Starting scheduled Twitter fetch');
      expect(mockFetchAndProcessTweets).toHaveBeenCalledTimes(1);
      expect(mockLogger.error).toHaveBeenCalledTimes(1);
      expect(mockLogger.error).toHaveBeenCalledWith('Error in Twitter fetch: Unknown error');
    });

    it('should handle errors with empty string message', async () => {
      // Arrange
      const testError = new Error('');
      mockFetchAndProcessTweets.mockRejectedValueOnce(testError);

      // Act
      await runTwitterFetch();

      // Assert
      expect(mockLogger.info).toHaveBeenCalledWith('Starting scheduled Twitter fetch');
      expect(mockFetchAndProcessTweets).toHaveBeenCalledTimes(1);
      expect(mockLogger.error).toHaveBeenCalledTimes(1);
      expect(mockLogger.error).toHaveBeenCalledWith('Error in Twitter fetch: Unknown error');
    });

    it('should call fetchAndProcessTweets exactly once per invocation', async () => {
      // Arrange
      mockFetchAndProcessTweets.mockResolvedValueOnce();

      // Act
      await runTwitterFetch();
      await runTwitterFetch();

      // Assert
      expect(mockFetchAndProcessTweets).toHaveBeenCalledTimes(2);
      expect(mockLogger.info).toHaveBeenCalledTimes(2);
    });

    it('should maintain scheduler stability by not throwing errors', async () => {
      // Arrange
      const criticalError = new Error('Critical system failure');
      mockFetchAndProcessTweets.mockRejectedValueOnce(criticalError);

      // Act & Assert - Function should complete without throwing
      await expect(runTwitterFetch()).resolves.toBeUndefined();

      // Verify the error was logged but not thrown
      expect(mockLogger.error).toHaveBeenCalledWith('Error in Twitter fetch: Critical system failure');
    });

    it('should handle async errors properly', async () => {
      // Arrange
      const asyncError = new Error('Async operation failed');
      mockFetchAndProcessTweets.mockImplementationOnce(async () => {
        await new Promise(resolve => setTimeout(resolve, 10));
        throw asyncError;
      });

      // Act
      await runTwitterFetch();

      // Assert
      expect(mockLogger.info).toHaveBeenCalledWith('Starting scheduled Twitter fetch');
      expect(mockLogger.error).toHaveBeenCalledWith('Error in Twitter fetch: Async operation failed');
    });
  });

  describe('module exports', () => {
    it('should export runTwitterFetch function', () => {
      expect(typeof runTwitterFetch).toBe('function');
    });

    it('should export only the expected functions', () => {
      const twitterScheduler = require('../utils/twitterScheduler');
      expect(Object.keys(twitterScheduler)).toEqual(['runTwitterFetch']);
    });
  });
});
