const axios = require('axios');
const {
  searchTweets,
  sendMonthlyPullsAlertToSlack
} = require('../utils/twitterClient');

jest.mock('axios');
jest.mock('../utils/logger', () => () => ({
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn()
  }));
jest.mock('../utils/databaseConfig', () => ({
  getNewsCentreConnection: jest.fn(() => ({
    query: jest.fn().mockResolvedValue({ rows: [{ last_seen_tweet_id: '12345' }, { total_pulls: 10 }] }),
    release: jest.fn()
  })),
  releaseNewsCentreConnection: jest.fn()
}));
jest.mock('../utils/constants', () => ({
  TWITTER_API_BASE: 'https://api.twitter.com/2/tweets/search/recent',
  TWITTER_AUTH_TOKEN: 'mock-token',
  TWITTER_SOURCE_ID: 'src123',
  MONTHLY_TARGET_PULLS: 500,
  TWITTER_PULL_START_DAY: 1,
  GET_TOTAL_PULLS_SINCE_STARTDATE: 'SELECT COUNT(*) as total_pulls FROM pulls',
  GET_LAST_SEEN_TWEET_ID_QUERY: 'SELECT * FROM tweet_id',
  INSERT_TWITTER_PULL_QUERY: '',
}));

describe('twitterClient.js', () => {
  beforeEach(() => jest.clearAllMocks());

  it('should fetch and format tweets correctly', async () => {
    axios.get.mockResolvedValue({
      data: {
        data: [
          { id: '1', text: 'sample', created_at: new Date(), author_id: 'u1', attachments: { media_keys: ['m1'] } }
        ],
        includes: {
          users: [{ id: 'u1', username: 'tester' }],
          media: [{ media_key: 'm1', type: 'photo', url: 'http://img.jpg' }]
        }
      }
    });

    const data = await searchTweets('from:tester', 5);
    expect(data.data[0]).toHaveProperty('author_id', 'tester');
    expect(data.data[0]).toHaveProperty('images');
  });

  it('should handle API errors gracefully', async () => {
    axios.get.mockRejectedValue(new Error('fail'));
    await expect(searchTweets('from:x', 1)).rejects.toThrow('fail');
  });

  it('should send Slack alert with pull summary', async () => {
    await expect(sendMonthlyPullsAlertToSlack()).resolves.not.toThrow();
  });

  it('should add since_id when lastSeenTweetId is available', async () => {
    axios.get.mockResolvedValue({
      data: {
        data: [{ id: '5', text: 'test', created_at: new Date(), author_id: 'u1' }],
        includes: { users: [], media: [] }
      }
    });
  
    await searchTweets('from:test', 5);
    // No assertion needed — just executing the path that adds `since_id`
  });
  
  it('should handle empty includes gracefully', async () => {
    axios.get.mockResolvedValue({
      data: {
        data: [{ id: '6', text: 'hello', created_at: new Date(), author_id: 'u2' }],
        includes: {}
      }
    });
  
    const result = await searchTweets('from:test', 5);
    expect(result.data[0]).toHaveProperty('author_id', 'u2');
  });
  
  it('should skip newestTweetId logic if no tweets found', async () => {
    axios.get.mockResolvedValue({ data: { data: [], includes: {} } });
    const result = await searchTweets('from:none', 5);
    expect(result.data.length).toBe(0);
  });
  
  it('should populate enhanced error with API details on generic error', async () => {
    const error = new Error('generic fail');
    axios.get.mockImplementation(() => {
      throw error;
    });
  
    try {
      await searchTweets('failquery', 1);
    } catch (e) {
      expect(e.message).toContain('Twitter API Error');
      expect(e.apiDetails).toEqual(
        expect.objectContaining({
          endpoint: expect.any(String),
          query: 'failquery',
          maxResults: 1
        })
      );
    }
  });
  
  it('should catch and log request setup errors without response/request', async () => {
    const setupError = new Error('Setup fail');
    delete setupError.response;
    delete setupError.request;
    axios.get.mockImplementation(() => {
      throw setupError;
    });
  
    await expect(searchTweets('setuperror', 1)).rejects.toThrow('Setup fail');
  });
  it('should return null if no last_seen_tweet_id in DB', async () => {
    const { getNewsCentreConnection } = require('../utils/databaseConfig');
    getNewsCentreConnection.mockResolvedValueOnce({
      query: jest.fn().mockResolvedValue({ rows: [] }),
      release: jest.fn()
    });
  
    const { searchTweets } = require('../utils/twitterClient');
    axios.get.mockResolvedValue({
      data: {
        data: [],
        includes: {}
      }
    });
  
    await searchTweets('from:nodata', 1);
  });
});
