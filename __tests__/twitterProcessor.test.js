const { fetchAndProcessTweets, buildQueryString } = require('../utils/twitterProcessor');
const twitterClient = require('../utils/twitterClient');
const newsDbManager = require('../utils/newsDbManager');
const modelIntegrator = require('../utils/modelIntegrator');
const slackPublisher = require('../utils/slackPublisher');
const mutedComboManager = require('../utils/mutedComboManager');
const publisher = require('../utils/publisher');
const constants = require('../utils/constants');


jest.mock('../utils/twitterClient');
jest.mock('../utils/newsDbManager');
jest.mock('../utils/modelIntegrator');
jest.mock('../utils/slackPublisher');
jest.mock('../utils/mutedComboManager');
jest.mock('../utils/publisher');

const mockTweet = (id, text = 'Ferry explosion', authorId = '123', createdAt = new Date().toISOString(), images = []) => ({
  id,
  text,
  author_id: authorId,
  created_at: createdAt,
  images
});

describe('Twitter Lambda - Core Tweet Processing Scenarios', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    constants.primaryKeywords = ['Ferry'];
    constants.secondaryKeywords = ['explosion'];
    constants.TWITTER_USERNAMES = ['shiptracker', 'marinewatch'];
    constants.BANNED_WORDS = ["damn", "hell", "fuck", "shit"];   

    mutedComboManager.getMutedVessels.mockResolvedValue([]);
    mutedComboManager.resetExpiredMutedCombos.mockResolvedValue(true);
    mutedComboManager.insertOrUpdateMutedCombo.mockResolvedValue(1);
  });

  it('should ingest all tweets', async () => {
    twitterClient.searchTweets.mockResolvedValue({ data: [mockTweet('1'), mockTweet('2')] });
    newsDbManager.checkArticleExists.mockResolvedValue(false);
    newsDbManager.insertNewsArticle.mockResolvedValue('articleId');
    newsDbManager.getAllCategory.mockResolvedValue([{ name: 'explosion' }]);
    modelIntegrator.processNewsArticle.mockResolvedValue([{
      is_relevant: true,
      vessels: [{ vessel_name: 'Evergreen', categories: ['explosion'], organizations: ['Org1'], region: ['Region1'] }]
    }]);
    publisher.publishMessage.mockResolvedValue(true);
    slackPublisher.sendSlackMessage.mockResolvedValue(true);

    await fetchAndProcessTweets();

    expect(newsDbManager.insertNewsArticle).toHaveBeenCalledTimes(2);
    expect(publisher.publishMessage).toHaveBeenCalledTimes(2);
    expect(slackPublisher.sendSlackMessage).toHaveBeenCalledWith(expect.stringContaining('Ingested: *2*'));
  });

  it('should skip if no tweets pulled', async () => {
    twitterClient.searchTweets.mockResolvedValue({ data: [] });
    await fetchAndProcessTweets();
    expect(newsDbManager.insertNewsArticle).not.toHaveBeenCalled();
    expect(slackPublisher.sendSlackMessage).toHaveBeenCalledWith(expect.stringContaining('pulled 0 tweets'));
  });

  it('should insert one and filter one tweet', async () => {
    twitterClient.searchTweets.mockResolvedValue({ data: [mockTweet('5'), mockTweet('6')] });
    newsDbManager.checkArticleExists.mockImplementation((url) =>
      url.includes('5') ? false : true
    );
    newsDbManager.insertNewsArticle.mockResolvedValue('articleId');
    newsDbManager.getAllCategory.mockResolvedValue([{ name: 'explosion' }]);
    modelIntegrator.processNewsArticle.mockResolvedValue([{
      is_relevant: true,
      vessels: [{ vessel_name: 'Evergreen', categories: ['explosion'], organizations: ['Org1'], region: ['Region1'] }]
    }]);
    publisher.publishMessage.mockResolvedValue(true);
    slackPublisher.sendSlackMessage.mockResolvedValue(true);

    await fetchAndProcessTweets();

    expect(newsDbManager.insertNewsArticle).toHaveBeenCalledTimes(1);
    expect(slackPublisher.sendSlackMessage).toHaveBeenCalledWith(expect.stringContaining('Ingested: *1*'));
    expect(slackPublisher.sendSlackMessage).toHaveBeenCalledWith(expect.stringContaining('Filtered Out: *1*'));
  });

  it('should treat tweets with same content but different IDs as unique', async () => {
    const tweetA = mockTweet('7', 'Ferry explosion');
    const tweetB = mockTweet('8', 'Ferry explosion');

    twitterClient.searchTweets.mockResolvedValue({ data: [tweetA, tweetB] });
    newsDbManager.checkArticleExists.mockResolvedValueOnce(false).mockResolvedValueOnce(false);
    newsDbManager.insertNewsArticle.mockResolvedValue('articleId');
    newsDbManager.getAllCategory.mockResolvedValue([{ name: 'explosion' }]);
    modelIntegrator.processNewsArticle.mockResolvedValue([{
      is_relevant: true,
      vessels: [{ vessel_name: 'Ship1', categories: ['explosion'], organizations: ['Org'], region: ['Region'] }]
    }]);
    publisher.publishMessage.mockResolvedValue(true);
    slackPublisher.sendSlackMessage.mockResolvedValue(true);

    await fetchAndProcessTweets();

    expect(newsDbManager.insertNewsArticle).toHaveBeenCalledTimes(2);
    expect(publisher.publishMessage).toHaveBeenCalledTimes(2);
  });

  it('should skip second tweet with duplicate ID', async () => {
    const tweet = mockTweet('9');

    twitterClient.searchTweets.mockResolvedValue({ data: [tweet, tweet] });
    newsDbManager.checkArticleExists.mockResolvedValueOnce(false).mockResolvedValueOnce(true);
    newsDbManager.insertNewsArticle.mockResolvedValue('articleId');
    newsDbManager.getAllCategory.mockResolvedValue([{ name: 'explosion' }]);
    modelIntegrator.processNewsArticle.mockResolvedValue([{
      is_relevant: true,
      vessels: [{ vessel_name: 'VesselDup', categories: ['explosion'], organizations: ['OrgDup'], region: ['RegionDup'] }]
    }]);
    publisher.publishMessage.mockResolvedValue(true);
    slackPublisher.sendSlackMessage.mockResolvedValue(true);

    await fetchAndProcessTweets();

    expect(newsDbManager.insertNewsArticle).toHaveBeenCalledTimes(1);
    expect(publisher.publishMessage).toHaveBeenCalledTimes(1);
  });

  it('should filter duplicate tweet by URL', async () => {
    const tweet = mockTweet('10');
    twitterClient.searchTweets.mockResolvedValue({ data: [tweet] });
    newsDbManager.checkArticleExists.mockResolvedValue(true);
    await fetchAndProcessTweets();
    expect(newsDbManager.insertNewsArticle).not.toHaveBeenCalled();
  });

  it('should filter muted vessel-keyword combos', async () => {
    mutedComboManager.insertOrUpdateMutedCombo.mockResolvedValue(11);
    twitterClient.searchTweets.mockResolvedValue({ data: [mockTweet('11')] });
    newsDbManager.checkArticleExists.mockResolvedValue(false);
    modelIntegrator.processNewsArticle.mockResolvedValue([{
      is_relevant: true,
      vessels: [{ vessel_name: 'MutedShip', categories: ['explosion'], organizations: [], region: [] }]
    }]);
    newsDbManager.getAllCategory.mockResolvedValue([{ name: 'explosion' }]);

    await fetchAndProcessTweets();
    expect(newsDbManager.insertNewsArticle).not.toHaveBeenCalled();
  });

  it('should filter tweets missing primary keyword', async () => {
    twitterClient.searchTweets.mockResolvedValue({ data: [mockTweet('13', 'only explosion')] });
    await fetchAndProcessTweets();
    expect(newsDbManager.insertNewsArticle).not.toHaveBeenCalled();
  });

  it('should filter tweets missing secondary keyword', async () => {
    twitterClient.searchTweets.mockResolvedValue({ data: [mockTweet('14', 'only Ferry')] });
    await fetchAndProcessTweets();
    expect(newsDbManager.insertNewsArticle).not.toHaveBeenCalled();
  });

  it('should filter tweets missing both keywords', async () => {
    twitterClient.searchTweets.mockResolvedValue({ data: [mockTweet('15', 'random noise')] });
    await fetchAndProcessTweets();
    expect(newsDbManager.insertNewsArticle).not.toHaveBeenCalled();
  });

  it('should filter tweets with unmatched category', async () => {
    twitterClient.searchTweets.mockResolvedValue({ data: [mockTweet('16')] });
    newsDbManager.checkArticleExists.mockResolvedValue(false);
    modelIntegrator.processNewsArticle.mockResolvedValue([{
      is_relevant: true,
      vessels: [{ vessel_name: 'VesselX', categories: ['explosion'] }]
    }]);
    newsDbManager.getAllCategory.mockResolvedValue([{ name: 'xyz' }]);
    await fetchAndProcessTweets();
    expect(newsDbManager.insertNewsArticle).not.toHaveBeenCalled();
  });

  it('should filter empty vessel array in model response', async () => {
    twitterClient.searchTweets.mockResolvedValue({ data: [mockTweet('17')] });
    newsDbManager.checkArticleExists.mockResolvedValue(false);
    modelIntegrator.processNewsArticle.mockResolvedValue([{ is_relevant: true, vessels: [] }]);
    await fetchAndProcessTweets();
    expect(newsDbManager.insertNewsArticle).not.toHaveBeenCalled();
  });

  it('should ingest tweet with keywords in hashtags', async () => {
    twitterClient.searchTweets.mockResolvedValue({ data: [mockTweet('18', '#Ferry #explosion near dock')] });
    newsDbManager.checkArticleExists.mockResolvedValue(false);
    newsDbManager.insertNewsArticle.mockResolvedValue('articleId');
    newsDbManager.getAllCategory.mockResolvedValue([{ name: 'explosion' }]);
    modelIntegrator.processNewsArticle.mockResolvedValue([{
      is_relevant: true,
      vessels: [{ vessel_name: 'HashShip', categories: ['explosion'], organizations: ['Org1'], region: ['R1'] }]
    }]);
    publisher.publishMessage.mockResolvedValue(true);
    await fetchAndProcessTweets();
    expect(newsDbManager.insertNewsArticle).toHaveBeenCalled();
  });

  it('should detect keywords with casing differences', async () => {
    twitterClient.searchTweets.mockResolvedValue({ data: [mockTweet('19', 'FeRrY EXPLOSION')] });
    newsDbManager.checkArticleExists.mockResolvedValue(false);
    newsDbManager.insertNewsArticle.mockResolvedValue('articleId');
    newsDbManager.getAllCategory.mockResolvedValue([{ name: 'explosion' }]);
    modelIntegrator.processNewsArticle.mockResolvedValue([{
      is_relevant: true,
      vessels: [{ vessel_name: 'CaseShip', categories: ['explosion'], organizations: ['Org1'], region: ['R1'] }]
    }]);
    publisher.publishMessage.mockResolvedValue(true);
    await fetchAndProcessTweets();
    expect(newsDbManager.insertNewsArticle).toHaveBeenCalled();
  });

  it('should detect keywords in text with emojis', async () => {
    twitterClient.searchTweets.mockResolvedValue({ data: [mockTweet('20', 'Ferry 💥 explosion')] });
    newsDbManager.checkArticleExists.mockResolvedValue(false);
    newsDbManager.insertNewsArticle.mockResolvedValue('articleId');
    newsDbManager.getAllCategory.mockResolvedValue([{ name: 'explosion' }]);
    modelIntegrator.processNewsArticle.mockResolvedValue([{
      is_relevant: true,
      vessels: [{ vessel_name: 'EmojiShip', categories: ['explosion'], organizations: ['Org1'], region: ['R1'] }]
    }]);
    publisher.publishMessage.mockResolvedValue(true);
    await fetchAndProcessTweets();
    expect(newsDbManager.insertNewsArticle).toHaveBeenCalled();
  });

  it('should ignore alt text-only image keywords', async () => {
    const imageWithAlt = [{ url: 'image.jpg', alt_text: 'Ferry explosion' }];
    twitterClient.searchTweets.mockResolvedValue({ data: [mockTweet('22', 'random text', '123', new Date().toISOString(), imageWithAlt)] });
    await fetchAndProcessTweets();
    expect(newsDbManager.insertNewsArticle).not.toHaveBeenCalled();
  });

  it('should include mocked usernames and banned words in the query', async () => {
    const testUsernames = ['mercoglianos', 'BartGonnissen'];
    const testBannedWords = ['hell'];
  
    const query = await buildQueryString({
      usernames: testUsernames,
      bannedWords: testBannedWords,
      getMutedVesselsFn: async () => []
    });
  
    expect(query).toContain('from:mercoglianos OR from:BartGonnissen');
    expect(query).toContain('-is:retweet');
    expect(query).toContain('-hell');
    expect(query.length).toBeLessThanOrEqual(512);
  });

  it('should handle Twitter query string exceeding 512 characters gracefully', async () => {
    constants.TWITTER_USERNAMES = new Array(100).fill('username_long_enough_to_exceed_query_limit');
    const query = await buildQueryString();
    expect(query.length).toBeLessThanOrEqual(512);
  });

  it('should ingest tweet containing special characters in text', async () => {
    const specialCharTweet = mockTweet('special1', 'Ferry explosion 🚢🔥💥');
    twitterClient.searchTweets.mockResolvedValue({ data: [specialCharTweet] });
    newsDbManager.checkArticleExists.mockResolvedValue(false);
    newsDbManager.getAllCategory.mockResolvedValue([{ name: 'explosion' }]);
    modelIntegrator.processNewsArticle.mockResolvedValue([{ is_relevant: true, vessels: [{ vessel_name: 'SpecialShip', categories: ['explosion'] }] }]);
    newsDbManager.insertNewsArticle.mockResolvedValue('articleId');
    publisher.publishMessage.mockResolvedValue(true);

    await fetchAndProcessTweets();
    expect(newsDbManager.insertNewsArticle).toHaveBeenCalled();
  });

  it('should handle Twitter API 500 Internal Server Error gracefully', async () => {
    twitterClient.searchTweets.mockRejectedValue(new Error('Internal Server Error'));
    await expect(fetchAndProcessTweets()).resolves.not.toThrow();
    expect(slackPublisher.sendSlackMessage).not.toHaveBeenCalledWith(expect.stringContaining('Ingested'));
  });

  it('should handle Twitter API Auth Token Expired scenario', async () => {
    twitterClient.searchTweets.mockRejectedValue(new Error('401 Unauthorized'));
    await expect(fetchAndProcessTweets()).resolves.not.toThrow();
    expect(slackPublisher.sendSlackMessage).not.toHaveBeenCalled();
  });
  
});
