const axios = require('axios');

// Mock logger first
const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
  debug: jest.fn(),
  warn: jest.fn()
};

jest.mock('../utils/logger', () => () => mockLogger);

// Mock database connection
const mockClient = {
  query: jest.fn(),
  release: jest.fn()
};

const mockGetConnection = jest.fn(() => Promise.resolve(mockClient));
const mockReleaseConnection = jest.fn();

jest.mock('../utils/databaseConfig', () => ({
  getNewsCentreConnection: mockGetConnection,
  releaseNewsCentreConnection: mockReleaseConnection
}));

// Mock Slack publisher
const mockSendSlackMessage = jest.fn();
jest.mock('../utils/slackPublisher', () => ({
  sendSlackMessage: mockSendSlackMessage
}));

jest.mock('../utils/constants', () => ({
  TWITTER_API_BASE: 'https://api.twitter.com/2/tweets/search/recent',
  TWITTER_AUTH_TOKEN: 'mock-token',
  TWITTER_SOURCE_ID: 'src123',
  MONTHLY_TARGET_PULLS: 500,
  TWITTER_PULL_START_DAY: 15,
  GET_TOTAL_PULLS_SINCE_STARTDATE: 'SELECT COUNT(*) as total_pulls FROM pulls',
  GET_LAST_SEEN_TWEET_ID_QUERY: 'SELECT * FROM tweet_id',
  INSERT_TWITTER_PULL_QUERY: 'INSERT INTO pulls...',
}));

jest.mock('axios');

const {
  searchTweets,
  sendMonthlyPullsAlertToSlack
} = require('../utils/twitterClient');

describe('twitterClient.js', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset default mock behavior
    mockClient.query.mockResolvedValue({ rows: [{ last_seen_tweet_id: '12345' }] });
    mockGetConnection.mockResolvedValue(mockClient);
    mockSendSlackMessage.mockResolvedValue();
  });

  it('should fetch and format tweets correctly', async () => {
    axios.get.mockResolvedValue({
      data: {
        data: [
          { id: '1', text: 'sample', created_at: new Date(), author_id: 'u1', attachments: { media_keys: ['m1'] } }
        ],
        includes: {
          users: [{ id: 'u1', username: 'tester' }],
          media: [{ media_key: 'm1', type: 'photo', url: 'http://img.jpg' }]
        }
      }
    });

    const data = await searchTweets('from:tester', 5);
    expect(data.data[0]).toHaveProperty('author_id', 'tester');
    expect(data.data[0]).toHaveProperty('images');
  });

  it('should handle API errors gracefully', async () => {
    axios.get.mockRejectedValue(new Error('fail'));
    await expect(searchTweets('from:x', 1)).rejects.toThrow('fail');
  });

  it('should send Slack alert with pull summary', async () => {
    await expect(sendMonthlyPullsAlertToSlack()).resolves.not.toThrow();
  });

  it('should add since_id when lastSeenTweetId is available', async () => {
    axios.get.mockResolvedValue({
      data: {
        data: [{ id: '5', text: 'test', created_at: new Date(), author_id: 'u1' }],
        includes: { users: [], media: [] }
      }
    });

    await searchTweets('from:test', 5);
    // No assertion needed — just executing the path that adds `since_id`
  });

  it('should handle empty includes gracefully', async () => {
    axios.get.mockResolvedValue({
      data: {
        data: [{ id: '6', text: 'hello', created_at: new Date(), author_id: 'u2' }],
        includes: {}
      }
    });

    const result = await searchTweets('from:test', 5);
    expect(result.data[0]).toHaveProperty('author_id', 'u2');
  });

  it('should skip newestTweetId logic if no tweets found', async () => {
    axios.get.mockResolvedValue({ data: { data: [], includes: {} } });
    const result = await searchTweets('from:none', 5);
    expect(result.data.length).toBe(0);
  });

  it('should populate enhanced error with API details on generic error', async () => {
    const error = new Error('generic fail');
    axios.get.mockImplementation(() => {
      throw error;
    });

    try {
      await searchTweets('failquery', 1);
    } catch (e) {
      expect(e.message).toContain('Twitter API Error');
      expect(e.apiDetails).toEqual(
        expect.objectContaining({
          endpoint: expect.any(String),
          query: 'failquery',
          maxResults: 1
        })
      );
    }
  });

  it('should catch and log request setup errors without response/request', async () => {
    const setupError = new Error('Setup fail');
    delete setupError.response;
    delete setupError.request;
    axios.get.mockImplementation(() => {
      throw setupError;
    });

    await expect(searchTweets('setuperror', 1)).rejects.toThrow('Setup fail');
  });
  it('should return null if no last_seen_tweet_id in DB', async () => {
    mockClient.query.mockResolvedValueOnce({ rows: [] });

    axios.get.mockResolvedValue({
      data: {
        data: [],
        includes: {}
      }
    });

    await searchTweets('from:nodata', 1);
    expect(mockLogger.info).toHaveBeenCalledWith('No lastSeenTweetId found in database');
  });

  // Additional tests to achieve 100% coverage

  describe('getLastSeenTweetId error handling', () => {
    it('should handle database connection error in getLastSeenTweetId', async () => {
      mockGetConnection.mockRejectedValueOnce(new Error('Connection failed'));

      axios.get.mockResolvedValue({
        data: { data: [], includes: {} }
      });

      await searchTweets('from:test', 1);

      expect(mockLogger.error).toHaveBeenCalledWith('Error retrieving lastSeenTweetId: Connection failed');
    });

    it('should handle database query error in getLastSeenTweetId', async () => {
      mockClient.query.mockRejectedValueOnce(new Error('Query failed'));

      axios.get.mockResolvedValue({
        data: { data: [], includes: {} }
      });

      await searchTweets('from:test', 1);

      expect(mockLogger.error).toHaveBeenCalledWith('Error retrieving lastSeenTweetId: Query failed');
      expect(mockReleaseConnection).toHaveBeenCalledWith(mockClient);
    });
  });

  describe('saveLastSeenTweetIdAndPulls error handling', () => {
    it('should handle database error in saveLastSeenTweetIdAndPulls', async () => {
      // First call for getLastSeenTweetId succeeds
      mockClient.query
        .mockResolvedValueOnce({ rows: [{ last_seen_tweet_id: '12345' }] })
        .mockRejectedValueOnce(new Error('Insert failed'));

      axios.get.mockResolvedValue({
        data: {
          data: [{ id: '67890', text: 'test', created_at: new Date(), author_id: 'u1' }],
          includes: { users: [{ id: 'u1', username: 'testuser' }] }
        }
      });

      await searchTweets('from:test', 1);

      expect(mockLogger.error).toHaveBeenCalledWith('Error recording tweet pull: Insert failed');
    });

    it('should handle early return in saveLastSeenTweetIdAndPulls when newestTweetId is null', async () => {
      // This test specifically targets line 37 in twitterClient.js: if (!lastSeenTweetId) return;
      // We need to test the case where saveLastSeenTweetIdAndPulls is called with a null newestTweetId

      // Mock getLastSeenTweetId to return a valid ID
      mockClient.query.mockResolvedValueOnce({ rows: [{ last_seen_tweet_id: '12345' }] });

      // Mock axios to return tweets with null/undefined IDs (edge case)
      axios.get.mockResolvedValue({
        data: {
          data: [{ id: null, text: 'test', created_at: new Date(), author_id: 'u1' }],
          includes: { users: [{ id: 'u1', username: 'testuser' }] }
        }
      });

      await searchTweets('from:test', 1);

      // Should call getLastSeenTweetId but saveLastSeenTweetIdAndPulls should return early
      // because newestTweetId will be null
      expect(mockClient.query).toHaveBeenCalledTimes(1);
    });
  });

  describe('sendMonthlyPullsAlertToSlack comprehensive tests', () => {
    beforeEach(() => {
      // Mock Date to have consistent test results
      jest.useFakeTimers();
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('should handle date calculation when current date is before TWITTER_PULL_START_DAY', async () => {
      // Set date to 10th of the month (before 15th)
      const mockDate = new Date('2024-03-10T10:00:00.000Z');
      jest.setSystemTime(mockDate);

      mockClient.query.mockResolvedValueOnce({ rows: [{ total_pulls: 250 }] });

      await sendMonthlyPullsAlertToSlack();

      expect(mockClient.query).toHaveBeenCalledWith(
        'SELECT COUNT(*) as total_pulls FROM pulls',
        [new Date('2024-02-15T00:00:00.000Z')] // Should use previous month
      );
      expect(mockSendSlackMessage).toHaveBeenCalledWith(
        expect.stringContaining('Pulled so far: *250*')
      );
    });

    it('should handle date calculation when current date is after TWITTER_PULL_START_DAY', async () => {
      // Set date to 20th of the month (after 15th)
      const mockDate = new Date('2024-03-20T10:00:00.000Z');
      jest.setSystemTime(mockDate);

      mockClient.query.mockResolvedValueOnce({ rows: [{ total_pulls: 300 }] });

      await sendMonthlyPullsAlertToSlack();

      expect(mockClient.query).toHaveBeenCalledWith(
        'SELECT COUNT(*) as total_pulls FROM pulls',
        [new Date('2024-03-15T00:00:00.000Z')] // Should use current month
      );
    });

    it('should handle year rollover when calculating previous month', async () => {
      // Set date to January 10th (before 15th, so should use December of previous year)
      const mockDate = new Date('2024-01-10T10:00:00.000Z');
      jest.setSystemTime(mockDate);

      mockClient.query.mockResolvedValueOnce({ rows: [{ total_pulls: 100 }] });

      await sendMonthlyPullsAlertToSlack();

      expect(mockClient.query).toHaveBeenCalledWith(
        'SELECT COUNT(*) as total_pulls FROM pulls',
        [new Date('2023-12-15T00:00:00.000Z')] // Should use December of previous year
      );
    });

    it('should handle database error in sendMonthlyPullsAlertToSlack', async () => {
      mockGetConnection.mockRejectedValueOnce(new Error('DB connection failed'));

      await sendMonthlyPullsAlertToSlack();

      expect(mockLogger.error).toHaveBeenCalledWith('Error sending monthly pulls alert to Slack: DB connection failed');
    });

    it('should handle null total_pulls from database', async () => {
      const mockDate = new Date('2024-03-20T10:00:00.000Z');
      jest.setSystemTime(mockDate);

      mockClient.query.mockResolvedValueOnce({ rows: [{ total_pulls: null }] });

      await sendMonthlyPullsAlertToSlack();

      expect(mockSendSlackMessage).toHaveBeenCalledWith(
        expect.stringContaining('Pulled so far: *0*')
      );
    });
  });

  describe('searchTweets error handling comprehensive tests', () => {
    it('should handle axios error with response object', async () => {
      const errorWithResponse = new Error('API Error');
      errorWithResponse.response = {
        status: 429,
        data: { error: 'Rate limit exceeded' },
        headers: { 'x-rate-limit': '0' }
      };

      axios.get.mockRejectedValueOnce(errorWithResponse);

      try {
        await searchTweets('from:test', 1);
      } catch (error) {
        expect(error.message).toContain('Twitter API Error: API Error');
        expect(error.apiDetails).toEqual({
          endpoint: 'https://api.twitter.com/2/tweets/search/recent',
          query: 'from:test',
          maxResults: 1
        });
      }

      expect(mockLogger.error).toHaveBeenCalledWith('Twitter API Error: API Error');
      expect(mockLogger.error).toHaveBeenCalledWith('Status code: 429');
      expect(mockLogger.error).toHaveBeenCalledWith('Response data: {"error":"Rate limit exceeded"}');
      expect(mockLogger.error).toHaveBeenCalledWith('Response headers: {"x-rate-limit":"0"}');
    });

    it('should handle axios error with request object but no response', async () => {
      const errorWithRequest = new Error('Network Error');
      errorWithRequest.request = { method: 'GET', url: 'https://api.twitter.com' };
      delete errorWithRequest.response;

      axios.get.mockRejectedValueOnce(errorWithRequest);

      try {
        await searchTweets('from:test', 1);
      } catch (error) {
        expect(error.message).toContain('Twitter API Error: Network Error');
      }

      expect(mockLogger.error).toHaveBeenCalledWith('No response received from Twitter API');
      expect(mockLogger.error).toHaveBeenCalledWith('Request details: {"method":"GET","url":"https://api.twitter.com"}');
    });

    it('should handle axios error without response or request', async () => {
      const setupError = new Error('Request setup error');
      delete setupError.response;
      delete setupError.request;

      axios.get.mockRejectedValueOnce(setupError);

      try {
        await searchTweets('from:test', 1);
      } catch (error) {
        expect(error.message).toContain('Twitter API Error: Request setup error');
      }

      expect(mockLogger.error).toHaveBeenCalledWith('Request setup error: Request setup error');
    });
  });

  describe('Edge cases and additional coverage', () => {
    it('should handle response without includes.media', async () => {
      axios.get.mockResolvedValue({
        data: {
          data: [{
            id: '1',
            text: 'test',
            created_at: '2024-03-20T10:00:00.000Z',
            author_id: 'u1',
            attachments: { media_keys: ['m1'] }
          }],
          includes: {
            users: [{ id: 'u1', username: 'testuser' }]
            // No media array
          }
        }
      });

      const result = await searchTweets('from:test', 1);

      expect(result.data[0].images).toEqual([]);
    });

    it('should handle media with non-photo type', async () => {
      axios.get.mockResolvedValue({
        data: {
          data: [{
            id: '1',
            text: 'test',
            created_at: '2024-03-20T10:00:00.000Z',
            author_id: 'u1',
            attachments: { media_keys: ['m1'] }
          }],
          includes: {
            users: [{ id: 'u1', username: 'testuser' }],
            media: [{ media_key: 'm1', type: 'video', url: 'http://video.mp4' }]
          }
        }
      });

      const result = await searchTweets('from:test', 1);

      expect(result.data[0].images).toEqual([]);
    });

    it('should handle tweets without attachments', async () => {
      axios.get.mockResolvedValue({
        data: {
          data: [{
            id: '1',
            text: 'test',
            created_at: '2024-03-20T10:00:00.000Z',
            author_id: 'u1'
            // No attachments
          }],
          includes: {
            users: [{ id: 'u1', username: 'testuser' }]
          }
        }
      });

      const result = await searchTweets('from:test', 1);

      expect(result.data[0].images).toEqual([]);
    });
  });
});
