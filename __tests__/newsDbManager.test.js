// __tests__/newsDbManager.test.js

const mockLogger = {
    error: jest.fn(),
    info: jest.fn(),
};

jest.mock('../utils/logger', () => () => mockLogger);

jest.mock('../utils/databaseConfig', () => ({
    getNewsCentreConnection: jest.fn(),
    releaseNewsCentreConnection: jest.fn()
}));

jest.mock('../utils/constants', () => ({
    CHECK_NEWS_URL_EXISTS_QUERY: 'SELECT EXISTS',
    INSERT_NEWS_ARTICLE: 'INSERT INTO news',
    GET_ALL_CATEGORY_QUERY: 'SELECT * FROM category',
    GET_ALL_ORGANISATION_QUERY: 'SELECT * FROM org',
    GET_ALL_REGION_QUERY: 'SELECT * FROM region',
    MONTHLY_TARGET_PULLS: 1000,
    TWITTER_PULL_START_DAY: 1,
    INTERVAL_MINUTES: 60
}));

const {
    checkArticleExists,
    insertNewsArticle,
    getAllCategory,
    getAllOrganisation,
    getAllRegion,
    calculatePullsPerInterval
} = require('../utils/newsDbManager');

const {
    getNewsCentreConnection,
    releaseNewsCentreConnection
} = require('../utils/databaseConfig');

// 🕒 Global Date mock for time-sensitive logic
const RealDate = Date;
beforeAll(() => {
    global.Date = class extends RealDate {
        constructor(...args) {
            if (args.length === 0) {
                return new RealDate('2025-05-15T10:00:00Z'); // consistent date
            }
            return new RealDate(...args);
        }
    };
});
afterAll(() => {
    global.Date = RealDate;
});

describe('newsDbManager', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should return true if article exists', async () => {
        const mockClient = {
            query: jest.fn().mockResolvedValue({ rows: [{ exists: true }] })
        };
        getNewsCentreConnection.mockResolvedValue(mockClient);

        const result = await checkArticleExists('http://example.com/article');
        expect(result).toBe(true);
        expect(mockClient.query).toHaveBeenCalledWith('SELECT EXISTS', ['http://example.com/article']);
        expect(releaseNewsCentreConnection).toHaveBeenCalledWith(mockClient);
    });

    it('should return false if article does not exist', async () => {
        const mockClient = {
            query: jest.fn().mockResolvedValue({ rows: [] })
        };
        getNewsCentreConnection.mockResolvedValue(mockClient);

        const result = await checkArticleExists('http://not-found.com');
        expect(result).toBe(false);
        expect(mockClient.query).toHaveBeenCalledWith('SELECT EXISTS', ['http://not-found.com']);
        expect(releaseNewsCentreConnection).toHaveBeenCalledWith(mockClient);
    });

    it('should insert a news article and return inserted ID', async () => {
        const mockClient = {
            query: jest.fn().mockResolvedValue({ rows: [{ id: 'article-id-123' }] })
        };
        getNewsCentreConnection.mockResolvedValue(mockClient);

        const article = {
            url: 'http://example.com',
            title: 'Breaking News',
            summary: 'Some summary text',
            publish_date: new Date(),
            source_id: 'Reuters',
            author: 'Jane Doe',
            images: []
        };

        const result = await insertNewsArticle(article);
        expect(result).toBe('article-id-123');
        expect(mockClient.query).toHaveBeenCalledWith(expect.any(String), expect.any(Array));
        expect(releaseNewsCentreConnection).toHaveBeenCalledWith(mockClient);
    });

    it('should return null if insert returns no rows', async () => {
        const mockClient = {
            query: jest.fn().mockResolvedValue({ rows: [] })
        };
        getNewsCentreConnection.mockResolvedValue(mockClient);

        const article = {
            url: 'http://example.com',
            title: 'Title',
            summary: 'Summary',
            publish_date: new Date(),
            source_id: 'Reuters',
            author: 'Someone',
            images: []
        };

        const result = await insertNewsArticle(article);
        expect(result).toBeNull();
    });

    it('should throw if title is empty', async () => {
        const article = {
            url: 'http://example.com',
            title: '',
            summary: 'Summary',
            publish_date: new Date(),
            source_id: 'Reuters',
            author: 'John',
            images: []
        };
        await expect(insertNewsArticle(article)).rejects.toThrow('Article title cannot be empty');
    });

    it('should throw if summary is empty', async () => {
        const article = {
            url: 'http://example.com',
            title: 'Title',
            summary: '',
            publish_date: new Date(),
            source_id: 'Reuters',
            author: 'John',
            images: []
        };
        await expect(insertNewsArticle(article)).rejects.toThrow('Article summary cannot be empty');
    });

    it('should return category list', async () => {
        const mockClient = {
            query: jest.fn().mockResolvedValue({ rows: [{ name: 'explosion', type: 'incident' }] })
        };
        getNewsCentreConnection.mockResolvedValue(mockClient);

        const result = await getAllCategory();
        expect(result).toEqual([{ name: 'explosion', type: 'incident' }]);
        expect(releaseNewsCentreConnection).toHaveBeenCalledWith(mockClient);
    });

    it('should return organisation list', async () => {
        const mockClient = {
            query: jest.fn().mockResolvedValue({ rows: [{ name: 'IMO', type: 'global' }] })
        };
        getNewsCentreConnection.mockResolvedValue(mockClient);

        const result = await getAllOrganisation();
        expect(result).toEqual([{ name: 'IMO', type: 'global' }]);
        expect(releaseNewsCentreConnection).toHaveBeenCalledWith(mockClient);
    });

    it('should return region list', async () => {
        const mockClient = {
            query: jest.fn().mockResolvedValue({ rows: [{ name: 'Gaza' }] })
        };
        getNewsCentreConnection.mockResolvedValue(mockClient);

        const result = await getAllRegion();
        expect(result).toEqual(['Gaza']);
        expect(mockClient.query).toHaveBeenCalled();
        expect(releaseNewsCentreConnection).toHaveBeenCalledWith(mockClient);
    });

    it('should throw and log error in checkArticleExists', async () => {
        const mockClient = { query: jest.fn().mockRejectedValue(new Error('Query failed')) };
        getNewsCentreConnection.mockResolvedValue(mockClient);

        await expect(checkArticleExists('bad')).rejects.toThrow('Query failed');
        expect(mockLogger.error).toHaveBeenCalledWith(expect.stringContaining('checkArticleExists'));
    });

    it('should throw and log error in getAllCategory', async () => {
        const mockClient = { query: jest.fn().mockRejectedValue(new Error('DB error')) };
        getNewsCentreConnection.mockResolvedValue(mockClient);

        await expect(getAllCategory()).rejects.toThrow('DB error');
        expect(mockLogger.error).toHaveBeenCalledWith(expect.stringContaining('getAllCategoryData'));
    });

    it('should throw and log error in getAllOrganisation', async () => {
        const mockClient = { query: jest.fn().mockRejectedValue(new Error('Org error')) };
        getNewsCentreConnection.mockResolvedValue(mockClient);

        await expect(getAllOrganisation()).rejects.toThrow('Org error');
        expect(mockLogger.error).toHaveBeenCalledWith(expect.stringContaining('getAllOrganisationData'));
    });

    it('should throw and log error in getAllRegion', async () => {
        const mockClient = { query: jest.fn().mockRejectedValue(new Error('Region error')) };
        getNewsCentreConnection.mockResolvedValue(mockClient);

        await expect(getAllRegion()).rejects.toThrow('Region error');
        expect(mockLogger.error).toHaveBeenCalledWith(expect.stringContaining('getAllRegionNames'));
    });

    it('should throw and log error in insertNewsArticle', async () => {
        const mockClient = {
            query: jest.fn().mockRejectedValue(new Error('Insert failed'))
        };
        getNewsCentreConnection.mockResolvedValue(mockClient);

        const article = {
            url: 'http://example.com',
            title: 'Valid',
            summary: 'Ok',
            publish_date: new Date(),
            source_id: 'Reuters',
            author: 'John',
            images: []
        };

        await expect(insertNewsArticle(article)).rejects.toThrow('Insert failed');
        expect(mockLogger.error).toHaveBeenCalledWith(expect.stringContaining('insertNewsArticle'));
    });

    it('should return 10 pulls when monthly target already achieved', async () => {
        const mockClient = {
            query: jest.fn().mockResolvedValue({ rows: [{ total_pulls: 1000 }] })
        };
        getNewsCentreConnection.mockResolvedValue(mockClient);

        const pulls = await calculatePullsPerInterval();
        expect(pulls).toBe(10);
    });

    it('should fallback to minimum 10 pulls if slotsLeft becomes 0', async () => {
        const mockClient = {
            query: jest.fn().mockResolvedValue({ rows: [{ total_pulls: 999 }] })
        };
        getNewsCentreConnection.mockResolvedValue(mockClient);

        const constants = require('../utils/constants');
        constants.INTERVAL_MINUTES = 9999999;

        const pulls = await calculatePullsPerInterval();
        expect(pulls).toBe(10);
    });

    it('should handle missing rows gracefully and default to minimum pulls', async () => {
        const mockClient = {
            query: jest.fn().mockResolvedValue({ rows: [] })
        };
        getNewsCentreConnection.mockResolvedValue(mockClient);

        const pulls = await calculatePullsPerInterval();
        expect(pulls).toBeGreaterThanOrEqual(10);
    });

    it('should not throw when releaseNewsCentreConnection is called with null', () => {
        expect(() => releaseNewsCentreConnection(null)).not.toThrow();
    });

    it('should handle lastMonth < 0 when today is January', async () => {
        global.Date = class extends Date {
            constructor(...args) {
                if (args.length === 0) {
                    return new RealDate('2025-01-15T10:00:00Z');
                }
                return new RealDate(...args);
            }
        };

        const mockClient = {
            query: jest.fn().mockResolvedValue({ rows: [{ total_pulls: 0 }] })
        };
        getNewsCentreConnection.mockResolvedValue(mockClient);
        const pulls = await calculatePullsPerInterval();
        expect(pulls).toBeGreaterThanOrEqual(10);
        global.Date = RealDate;
    });

    it('should handle database connection error in checkArticleExists', async () => {
        getNewsCentreConnection.mockRejectedValue(new Error('Connection failed'));

        await expect(checkArticleExists('http://test.com')).rejects.toThrow('Connection failed');
        expect(mockLogger.error).toHaveBeenCalledWith(expect.stringContaining('checkArticleExists'));
    });

    it('should handle database connection error in insertNewsArticle', async () => {
        getNewsCentreConnection.mockRejectedValue(new Error('Connection failed'));

        const article = {
            url: 'http://example.com',
            title: 'Valid Title',
            summary: 'Valid Summary',
            publish_date: new Date(),
            source_id: 'Reuters',
            author: 'John',
            images: []
        };

        await expect(insertNewsArticle(article)).rejects.toThrow('Connection failed');
        expect(mockLogger.error).toHaveBeenCalledWith(expect.stringContaining('insertNewsArticle'));
    });

    it('should handle database connection error in getAllCategory', async () => {
        getNewsCentreConnection.mockRejectedValue(new Error('Connection failed'));

        await expect(getAllCategory()).rejects.toThrow('Connection failed');
        expect(mockLogger.error).toHaveBeenCalledWith(expect.stringContaining('getAllCategoryData'));
    });

    it('should handle database connection error in getAllOrganisation', async () => {
        getNewsCentreConnection.mockRejectedValue(new Error('Connection failed'));

        await expect(getAllOrganisation()).rejects.toThrow('Connection failed');
        expect(mockLogger.error).toHaveBeenCalledWith(expect.stringContaining('getAllOrganisationData'));
    });

    it('should handle database connection error in getAllRegion', async () => {
        getNewsCentreConnection.mockRejectedValue(new Error('Connection failed'));

        await expect(getAllRegion()).rejects.toThrow('Connection failed');
        expect(mockLogger.error).toHaveBeenCalledWith(expect.stringContaining('getAllRegionNames'));
    });

    it('should handle database connection error in calculatePullsPerInterval', async () => {
        getNewsCentreConnection.mockRejectedValue(new Error('Connection failed'));

        await expect(calculatePullsPerInterval()).rejects.toThrow('Connection failed');
    });

    it('should handle article with standard fields', async () => {
        const mockClient = {
            query: jest.fn().mockResolvedValue({ rows: [{ id: 'article-standard' }] })
        };
        getNewsCentreConnection.mockResolvedValue(mockClient);

        const article = {
            url: 'http://example.com',
            title: 'Standard Article',
            summary: 'Standard Summary',
            publish_date: new Date(),
            source_id: 'Twitter',
            author: 'User',
            images: ['image1.jpg', 'image2.jpg']
        };

        const result = await insertNewsArticle(article);
        expect(result).toBe('article-standard');

        // Check that all parameters were passed correctly
        const queryCall = mockClient.query.mock.calls[0];
        const params = queryCall[1];
        expect(params[0]).toBe(article.url);
        expect(params[1]).toBe(article.source_id);
        expect(params[2]).toBe(article.title);
        expect(params[3]).toBe(article.summary);
        expect(params[4]).toBe(article.publish_date);
        expect(params[5]).toBe(article.author);
        expect(params[6]).toEqual(article.images);
    });

    it('should handle article with empty images array', async () => {
        const mockClient = {
            query: jest.fn().mockResolvedValue({ rows: [{ id: 'article-no-images' }] })
        };
        getNewsCentreConnection.mockResolvedValue(mockClient);

        const article = {
            url: 'http://example.com',
            title: 'Article without images',
            summary: 'Summary without images',
            publish_date: new Date(),
            source_id: 'Twitter',
            author: 'User',
            images: []
        };

        const result = await insertNewsArticle(article);
        expect(result).toBe('article-no-images');

        // Check that empty images array was passed correctly
        const queryCall = mockClient.query.mock.calls[0];
        const params = queryCall[1];
        expect(params[6]).toEqual([]);
    });

    it('should handle article with null author', async () => {
        const mockClient = {
            query: jest.fn().mockResolvedValue({ rows: [{ id: 'article-null-author' }] })
        };
        getNewsCentreConnection.mockResolvedValue(mockClient);

        const article = {
            url: 'http://example.com',
            title: 'Article with null author',
            summary: 'Summary with null author',
            publish_date: new Date(),
            source_id: 'Twitter',
            author: null,
            images: []
        };

        const result = await insertNewsArticle(article);
        expect(result).toBe('article-null-author');

        // Check that null author was passed correctly
        const queryCall = mockClient.query.mock.calls[0];
        const params = queryCall[1];
        expect(params[5]).toBeNull();
    });

});
